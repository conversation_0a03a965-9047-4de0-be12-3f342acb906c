
'Attribute VB_Name = "modDebug"
Option Explicit

' =========================================================
' Module: modDebug
' Description: Provides debugging and logging functionality
' =========================================================

' Debug level constants
Public Const DEBUG_LEVEL_ERROR As Integer = 1    ' Log errors only
Public Const DEBUG_LEVEL_WARNING As Integer = 2  ' Log errors and warnings
Public Const DEBUG_LEVEL_INFO As Integer = 3     ' Log errors, warnings, and info
Public Const DEBUG_LEVEL_VERBOSE As Integer = 4  ' Log everything, including detailed traces

' Debug configuration variables
Public IsDebugEnabled As Boolean     ' 是否开启debug模式
Public DebugLevel As Integer         ' 当前debug级别
Public IsFileLoggingEnabled As Boolean  ' 是否启用文件日志
Public IsImmediateOutputEnabled As Boolean ' 是否输出到即时窗口

' Log file related variables
Private LogFilePath As String    ' Log file path
Private UTF8_ENCODING As Boolean ' Whether to use UTF-8 encoding for log files

' Initialize debug module
Public Sub InitDebug(Optional level As Integer = -1, Optional enableDebug As Boolean = True, _
                    Optional enableFileLogging As Boolean = True, Optional enableImmediateOutput As Boolean = False, _
                    Optional logFilePath As String = "")
    On Error Resume Next

    ' 设置是否开启debug模式（如果未指定，使用配置值）
    IsDebugEnabled = IIf(enableDebug, True, CBool(GetConfig("EnableDebug", False)))

    ' 如果debug模式关闭，则直接退出
    If Not IsDebugEnabled Then
        Exit Sub
    End If

    ' 设置debug级别（如果未指定或指定为-1，使用配置值）
    If level = -1 Then
        DebugLevel = CInt(GetConfig("DebugLevel", 4))
    Else
        DebugLevel = level
    End If

    ' 确保debug级别在有效范围内
    If DebugLevel < DEBUG_LEVEL_ERROR Or DebugLevel > DEBUG_LEVEL_VERBOSE Then
        DebugLevel = DEBUG_LEVEL_VERBOSE ' 默认使用最详细级别
    End If

    ' 初始化文件日志设置
    IsFileLoggingEnabled = enableFileLogging

    ' 初始化即时窗口输出设置
    IsImmediateOutputEnabled = enableImmediateOutput

    ' 设置UTF-8编码选项
    UTF8_ENCODING = CBool(GetConfig("UTF8Encoding", True))

    ' 如果启用了文件日志
    If IsFileLoggingEnabled Then
        ' 如果文件路径未指定，使用默认路径
        If logFilePath = "" Then
            ' 检查ThisWorkbook.Path是否可用
            If ThisWorkbook.Path = "" Then
                ' 工作簿可能未保存，使用临时文件夹
                LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                Debug.Print "工作簿未保存，使用临时路径: " & LogFilePath
            Else
                LogFilePath = ThisWorkbook.Path & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                Debug.Print "使用默认日志路径: " & LogFilePath
            End If
        Else
            LogFilePath = logFilePath
            Debug.Print "使用指定日志路径: " & LogFilePath
        End If

        ' 检查路径是否包含无效字符
        If InStr(LogFilePath, "?") > 0 Or InStr(LogFilePath, "*") > 0 Or _
           InStr(LogFilePath, ":") > 3 Or InStr(LogFilePath, "<") > 0 Or _
           InStr(LogFilePath, ">") > 0 Or InStr(LogFilePath, "|") > 0 Or _
           InStr(LogFilePath, Chr(34)) > 0 Then
            ' 路径包含无效字符，使用临时文件夹
            LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
            Debug.Print "原路径包含无效字符，使用临时路径: " & LogFilePath
        End If

        ' 初始化日志文件
        InitLogFile
    End If

    ' 记录初始化信息
    LogInfo "Debug system initialized, level: " & GetDebugLevelName(DebugLevel) & _
            ", debug mode: " & IIf(IsDebugEnabled, "enabled", "disabled") & _
            ", file logging: " & IIf(IsFileLoggingEnabled, "enabled (" & LogFilePath & ")", "disabled") & _
            ", immediate output: " & IIf(IsImmediateOutputEnabled, "enabled", "disabled")
End Sub

' Initialize log file
Private Sub InitLogFile()
    On Error Resume Next

    ' 声明所有变量
    Dim stream As Object
    Dim headerText As String
    Dim fileNum As Integer  ' 只声明一次

    If UTF8_ENCODING Then
        ' Use ADODB.Stream for UTF-8 encoding
        Set stream = CreateObject("ADODB.Stream")

        ' 设置流的属性
        stream.Type = 2 ' 指定为文本流
        stream.Charset = "utf-8" ' 设置编码为 UTF-8
        stream.Open

        ' 写入日志文件头
        headerText = "====================================================" & vbCrLf & _
                    "  Project Management Gantt Chart System - Debug Log" & vbCrLf & _
                    "  Start time: " & Format(Now, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
                    "  Debug level: " & GetDebugLevelName(DebugLevel) & vbCrLf & _
                    "====================================================" & vbCrLf & vbCrLf & _
                    "Time              | Type   | Source                    | Message" & vbCrLf & _
                    String(100, "-") & vbCrLf

        stream.WriteText headerText

        ' 保存文件
        stream.SaveToFile LogFilePath, 2 ' 2 = 覆盖现有文件
        stream.Close
        Set stream = Nothing

        ' 检查是否有错误
        If Err.Number <> 0 Then
            Debug.Print "UTF-8日志初始化错误: " & Err.Number & " - " & Err.Description

            ' 回退到传统方式
            fileNum = FreeFile
            Open LogFilePath For Output As #fileNum
            Print #fileNum, "===================================================="
            Print #fileNum, "  Project Management Gantt Chart System - Debug Log"
            Print #fileNum, "  Start time: " & Format(Now, "yyyy-mm-dd hh:mm:ss")
            Print #fileNum, "  Debug level: " & GetDebugLevelName(DebugLevel)
            Print #fileNum, "===================================================="
            Print #fileNum, ""
            Print #fileNum, "Time              | Type   | Source                    | Message"
            Print #fileNum, String(100, "-")
            Close #fileNum
        End If
    Else
        ' 传统方式 - 使用标准VBA文件I/O
        fileNum = FreeFile

        ' 打开日志文件进行写入
        Open LogFilePath For Output As #fileNum

        ' 写入日志文件头
        Print #fileNum, "===================================================="
        Print #fileNum, "  Project Management Gantt Chart System - Debug Log"
        Print #fileNum, "  Start time: " & Format(Now, "yyyy-mm-dd hh:mm:ss")
        Print #fileNum, "  Debug level: " & GetDebugLevelName(DebugLevel)
        Print #fileNum, "===================================================="
        Print #fileNum, ""
        Print #fileNum, "Time              | Type   | Source                    | Message"
        Print #fileNum, String(100, "-")

        ' 关闭文件
        Close #fileNum
    End If

    On Error GoTo 0
End Sub

' Log error information
Public Sub LogError(errNumber As Long, errDescription As String, errSource As String)
    If DebugLevel >= DEBUG_LEVEL_ERROR Then
        WriteToLog "ERROR", errNumber & " - " & errDescription, errSource
    End If
End Sub

' Log warning information
Public Sub LogWarning(message As String, Optional source As String = "")
    If DebugLevel >= DEBUG_LEVEL_WARNING Then
        WriteToLog "WARNING", message, source
    End If
End Sub

' Log general information
Public Sub LogInfo(message As String, Optional source As String = "")
    If DebugLevel >= DEBUG_LEVEL_INFO Then
        WriteToLog "INFO", message, source
    End If
End Sub

' Log detailed trace information
Public Sub LogVerbose(message As String, Optional source As String = "")
    If DebugLevel >= DEBUG_LEVEL_VERBOSE Then
        WriteToLog "VERBOSE", message, source
    End If
End Sub

' Log function entry
Public Sub LogFunctionEntry(functionName As String, Optional params As String = "")
    If DebugLevel >= DEBUG_LEVEL_VERBOSE Then
        If params = "" Then
            WriteToLog "ENTER", "Execution started", functionName
        Else
            WriteToLog "ENTER", "Execution started, parameters: " & params, functionName
        End If
    End If
End Sub

' Log function exit
Public Sub LogFunctionExit(functionName As String, Optional result As String = "")
    If DebugLevel >= DEBUG_LEVEL_VERBOSE Then
        If result = "" Then
            WriteToLog "EXIT", "Execution completed", functionName
        Else
            WriteToLog "EXIT", "Execution completed, result: " & result, functionName
        End If
    End If
End Sub

' 移除了工作表日志相关函数

' 移除了导出工作表日志的函数

' Open log file
Public Sub OpenLogFile()
    On Error Resume Next

    If IsFileLoggingEnabled And LogFilePath <> "" Then
        ' Use system default program to open log file
        ' 检查文件是否存在
        Dim fso As Object
        Set fso = CreateObject("Scripting.FileSystemObject")

        If fso.FileExists(LogFilePath) Then
            Shell "explorer.exe """ & LogFilePath & """", vbNormalFocus
        Else
            MsgBox "日志文件不存在: " & LogFilePath, vbExclamation, "Debug"
        End If

        Set fso = Nothing
    Else
        MsgBox "Log file not enabled or path invalid", vbExclamation, "Debug"
    End If
End Sub



' Reinitialize log file
Public Sub ReinitializeLog()
    On Error Resume Next

    ' 如果debug模式关闭，则直接退出
    If Not IsDebugEnabled Then
        Exit Sub
    End If

    ' 保存当前配置
    Dim currentDebugLevel As Integer
    Dim currentImmediateOutput As Boolean

    currentDebugLevel = DebugLevel
    currentImmediateOutput = IsImmediateOutputEnabled

    ' 如果日志文件已启用，先关闭它（但不添加任务状态信息）
    If IsFileLoggingEnabled Then
        ' 直接禁用文件日志，不调用 CloseLogFile
        IsFileLoggingEnabled = False
    End If

    ' 创建新的日志文件名
    ' 检查ThisWorkbook.Path是否可用
    If ThisWorkbook.Path = "" Then
        ' 工作簿可能未保存，使用临时文件夹
        LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
        Debug.Print "工作簿未保存，使用临时路径: " & LogFilePath
    Else
        LogFilePath = ThisWorkbook.Path & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
        Debug.Print "使用默认日志路径: " & LogFilePath
    End If

    ' 检查路径是否包含无效字符
    If InStr(LogFilePath, "?") > 0 Or InStr(LogFilePath, "*") > 0 Or _
       InStr(LogFilePath, ":") > 3 Or InStr(LogFilePath, "<") > 0 Or _
       InStr(LogFilePath, ">") > 0 Or InStr(LogFilePath, "|") > 0 Or _
       InStr(LogFilePath, Chr(34)) > 0 Then
        ' 路径包含无效字符，使用临时文件夹
        LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
        Debug.Print "原路径包含无效字符，使用临时路径: " & LogFilePath
    End If

    ' 重新启用文件日志
    IsFileLoggingEnabled = True

    ' 初始化日志文件
    InitLogFile

    ' 记录重新初始化信息
    LogInfo "Debug system reinitialized, level: " & GetDebugLevelName(DebugLevel) & _
            ", debug mode: " & IIf(IsDebugEnabled, "enabled", "disabled") & _
            ", file logging: " & IIf(IsFileLoggingEnabled, "enabled (" & LogFilePath & ")", "disabled") & _
            ", immediate output: " & IIf(IsImmediateOutputEnabled, "enabled", "disabled")
End Sub

' Close log file
Public Sub CloseLogFile()
    On Error Resume Next

    ' 如果debug模式关闭或文件日志未启用，则直接退出
    If Not IsDebugEnabled Or Not IsFileLoggingEnabled Then
        Exit Sub
    End If

    ' 声明所有变量
    Dim footerText As String
    Dim stream As Object
    Dim binData As Variant
    Dim fso As Object
    Dim appendStream As Object
    Dim fileNum As Integer  ' 只声明一次
    Dim exitStatus As String

    ' 简化退出状态逻辑
    exitStatus = "NORMAL COMPLETION"

    ' 写入日志文件尾部
    footerText = vbCrLf & _
                "====================================================" & vbCrLf & _
                "  End time: " & Format(Now, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
                "  Exit status: " & exitStatus & vbCrLf & _
                "====================================================" & vbCrLf

        If UTF8_ENCODING Then
            ' 使用 ADODB.Stream 以 UTF-8 编码追加日志尾部
            On Error Resume Next

            Set stream = CreateObject("ADODB.Stream")

            ' 设置流属性
            stream.Type = 2 ' 文本
            stream.Charset = "utf-8"
            stream.Open

            ' 写入尾部文本
            stream.WriteText footerText

            ' 转换为二进制以便追加
            stream.Position = 0
            stream.Type = 1 ' 切换到二进制模式
            binData = stream.Read ' 读取所有二进制数据

            ' 关闭当前流
            stream.Close

            ' 检查文件是否存在
            Set fso = CreateObject("Scripting.FileSystemObject")

            If fso.FileExists(LogFilePath) Then
                ' 打开文件进行追加
                Set appendStream = CreateObject("ADODB.Stream")
                appendStream.Type = 1 ' 二进制
                appendStream.Open
                appendStream.LoadFromFile LogFilePath
                appendStream.Position = appendStream.Size ' 移动到文件末尾
                appendStream.Write binData ' 追加新内容
                appendStream.SaveToFile LogFilePath, 2 ' 覆盖原文件
                appendStream.Close
                Set appendStream = Nothing
            End If

            Set stream = Nothing
            Set fso = Nothing

            ' 如果有错误，回退到传统方式
            If Err.Number <> 0 Then
                Debug.Print "UTF-8日志关闭错误: " & Err.Number & " - " & Err.Description

                ' 回退到传统方式
                fileNum = FreeFile
                On Error Resume Next
                Open LogFilePath For Append As #fileNum

                ' 检查是否有错误（可能是路径问题）
                If Err.Number <> 0 Then
                    Debug.Print "无法打开日志文件进行追加: " & Err.Number & " - " & Err.Description
                    Debug.Print "尝试路径: " & LogFilePath

                    ' 尝试在临时文件夹创建日志文件
                    LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                    Debug.Print "尝试使用临时路径: " & LogFilePath

                    Err.Clear
                    Open LogFilePath For Output As #fileNum

                    ' 如果仍然失败，禁用文件日志
                    If Err.Number <> 0 Then
                        Debug.Print "无法创建日志文件，禁用文件日志"
                        IsFileLoggingEnabled = False
                        Close #fileNum
                        Exit Sub
                    End If
                End If
                On Error GoTo 0

                Print #fileNum, ""
                Print #fileNum, "===================================================="
                Print #fileNum, "  End time: " & Format(Now, "yyyy-mm-dd hh:mm:ss")
                Print #fileNum, "===================================================="
                Close #fileNum
            End If

            On Error GoTo 0
        Else
            ' 传统方式 - 使用标准VBA文件I/O
            fileNum = FreeFile

            ' 打开文件进行追加
            On Error Resume Next
            Open LogFilePath For Append As #fileNum

            ' 检查是否有错误（可能是路径问题）
            If Err.Number <> 0 Then
                Debug.Print "无法打开日志文件进行追加: " & Err.Number & " - " & Err.Description
                Debug.Print "尝试路径: " & LogFilePath

                ' 尝试在临时文件夹创建日志文件
                LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                Debug.Print "尝试使用临时路径: " & LogFilePath

                Err.Clear
                Open LogFilePath For Output As #fileNum

                ' 如果仍然失败，禁用文件日志
                If Err.Number <> 0 Then
                    Debug.Print "无法创建日志文件，禁用文件日志"
                    IsFileLoggingEnabled = False
                    Close #fileNum
                    Exit Sub
                End If
            End If
            On Error GoTo 0

            ' 写入尾部
            Print #fileNum, ""
            Print #fileNum, "===================================================="
            Print #fileNum, "  End time: " & Format(Now, "yyyy-mm-dd hh:mm:ss")
            Print #fileNum, "===================================================="

            ' 关闭文件
            Close #fileNum
        End If

        LogInfo "Log file closed: " & LogFilePath

    IsFileLoggingEnabled = False
End Sub

' ---------------------------------------------------------
' Helper Functions
' ---------------------------------------------------------

' Write log to worksheet and file
Private Sub WriteToLog(logType As String, message As String, source As String)
    ' 如果debug模式关闭，则直接退出
    If Not IsDebugEnabled Then
        Exit Sub
    End If

    On Error Resume Next

    ' 声明所有变量
    Dim timeStr As String
    Dim logLine As String
    Dim stream As Object
    Dim fso As Object
    Dim binData As Variant
    Dim appendStream As Object
    Dim fileNum As Integer  ' 只声明一次

    ' Format time string
    timeStr = Format(Now, "yyyy-mm-dd hh:mm:ss")

    ' 格式化日志行
    logLine = timeStr & " | " & _
             PadRight(logType, 8) & " | " & _
             PadRight(source, 25) & " | " & _
             message

    ' 1. Write to log file
    If IsFileLoggingEnabled Then
        On Error Resume Next

        If UTF8_ENCODING Then
            ' 使用 ADODB.Stream 以 UTF-8 编码追加日志
            On Error Resume Next

            Set stream = CreateObject("ADODB.Stream")

            ' 设置流属性
            stream.Type = 2 ' 文本
            stream.Charset = "utf-8"
            stream.Open

            ' 检查文件是否存在
            Set fso = CreateObject("Scripting.FileSystemObject")

            If fso.FileExists(LogFilePath) Then
                ' 文件存在，使用追加模式
                ' 先写入新内容到流
                stream.WriteText logLine & vbCrLf

                ' 转换为二进制以便追加
                stream.Position = 0
                stream.Type = 1 ' 切换到二进制模式
                binData = stream.Read ' 读取所有二进制数据

                ' 关闭当前流
                stream.Close

                ' 打开文件进行追加
                Set appendStream = CreateObject("ADODB.Stream")
                appendStream.Type = 1 ' 二进制
                appendStream.Open
                appendStream.LoadFromFile LogFilePath
                appendStream.Position = appendStream.Size ' 移动到文件末尾
                appendStream.Write binData ' 追加新内容
                appendStream.SaveToFile LogFilePath, 2 ' 覆盖原文件
                appendStream.Close
                Set appendStream = Nothing
            Else
                ' 文件不存在，创建新文件
                stream.WriteText logLine & vbCrLf
                stream.SaveToFile LogFilePath, 2 ' 覆盖
                stream.Close
            End If

            Set stream = Nothing
            Set fso = Nothing

            ' 如果有错误，回退到传统方式
            If Err.Number <> 0 Then
                ' 记录错误到立即窗口
                Debug.Print "UTF-8日志写入错误: " & Err.Number & " - " & Err.Description

                ' 回退到传统方式
                fileNum = FreeFile
                On Error Resume Next
                Open LogFilePath For Append As #fileNum

                ' 检查是否有错误（可能是路径问题）
                If Err.Number <> 0 Then
                    Debug.Print "无法打开日志文件进行追加: " & Err.Number & " - " & Err.Description
                    Debug.Print "尝试路径: " & LogFilePath

                    ' 尝试在临时文件夹创建日志文件
                    LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                    Debug.Print "尝试使用临时路径: " & LogFilePath

                    Err.Clear
                    Open LogFilePath For Output As #fileNum

                    ' 如果仍然失败，禁用文件日志
                    If Err.Number <> 0 Then
                        Debug.Print "无法创建日志文件，禁用文件日志"
                        IsFileLoggingEnabled = False
                        Close #fileNum
                        Exit Sub
                    End If
                End If
                On Error GoTo 0

                Print #fileNum, logLine
                Close #fileNum
            End If

            On Error GoTo 0
        Else
            ' 传统方式 - 使用标准VBA文件I/O
            fileNum = FreeFile

            ' 打开文件进行追加
            On Error Resume Next
            Open LogFilePath For Append As #fileNum

            ' 检查是否有错误（可能是路径问题）
            If Err.Number <> 0 Then
                Debug.Print "无法打开日志文件进行追加: " & Err.Number & " - " & Err.Description
                Debug.Print "尝试路径: " & LogFilePath

                ' 尝试在临时文件夹创建日志文件
                LogFilePath = Environ("TEMP") & "\DebugLog_" & Format(Now, "yyyymmdd_hhmmss") & ".txt"
                Debug.Print "尝试使用临时路径: " & LogFilePath

                Err.Clear
                Open LogFilePath For Output As #fileNum

                ' 如果仍然失败，禁用文件日志
                If Err.Number <> 0 Then
                    Debug.Print "无法创建日志文件，禁用文件日志"
                    IsFileLoggingEnabled = False
                    Close #fileNum
                    Exit Sub
                End If
            End If
            On Error GoTo 0

            ' 写入日志行
            Print #fileNum, logLine

            ' 关闭文件
            Close #fileNum
        End If
    End If

    ' 2. Output to immediate window (如果启用)
    If IsImmediateOutputEnabled Then
        Debug.Print timeStr & " | " & _
                    logType & " | " & _
                    source & " | " & _
                    message
    End If
End Sub

' Right-pad string to specified length
Private Function PadRight(str As String, length As Integer) As String
    If Len(str) >= length Then
        PadRight = Left(str, length)
    Else
        PadRight = str & Space(length - Len(str))
    End If
End Function

' 移除了创建和检查工作表的函数

' Get debug level name
Private Function GetDebugLevelName(level As Integer) As String
    Select Case level
        Case DEBUG_LEVEL_ERROR
            GetDebugLevelName = "ERROR"
        Case DEBUG_LEVEL_WARNING
            GetDebugLevelName = "WARNING"
        Case DEBUG_LEVEL_INFO
            GetDebugLevelName = "INFO"
        Case DEBUG_LEVEL_VERBOSE
            GetDebugLevelName = "VERBOSE"
        Case Else
            GetDebugLevelName = "UNKNOWN(" & level & ")"
    End Select
End Function
