# modDebug 模块设计文档

## 1. 模块概述

modDebug模块提供系统的调试和日志记录功能，用于跟踪系统运行状态、记录错误信息、辅助开发和故障排除。它支持多种日志级别，可以将日志信息输出到文本文件和立即窗口。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modDebug {
        +DEBUG_LEVEL_ERROR : Integer
        +DEBUG_LEVEL_WARNING : Integer
        +DEBUG_LEVEL_INFO : Integer
        +DEBUG_LEVEL_VERBOSE : Integer
        +IsDebugEnabled : Boolean
        +DebugLevel : Integer
        +IsFileLoggingEnabled : Boolean
        +IsImmediateOutputEnabled : Boolean
        +InitDebug(level, enableDebug, enableFileLogging, enableImmediateOutput, logFilePath)
        +LogError(errNumber, errDescription, errSource)
        +LogWarning(message, source)
        +LogInfo(message, source)
        +LogVerbose(message, source)
        +LogFunctionEntry(functionName, params)
        +LogFunctionExit(functionName, result)
        +OpenLogFile()
        +ReinitializeLog()
        +CloseLogFile()
        -WriteToLog(logType, message, source)
        -PadRight(str, length) : String
        -GetDebugLevelName(level) : String
    }
```

## 3. 主要功能

### 3.1 日志记录流程

```mermaid
flowchart TD
    Start([开始]) --> LogFunction[日志记录函数]
    LogFunction --> CheckLevel{检查日志级别}
    CheckLevel -->|级别不足| End([结束])
    CheckLevel -->|级别足够| WriteToLog[写入日志]

    WriteToLog --> WriteToFile{是否启用文件日志?}
    WriteToLog --> WriteToImmediateWindow[输出到即时窗口]

    WriteToFile -->|是| WriteToLogFile[写入日志文件]
    WriteToFile -->|否| End

    WriteToLogFile --> End
    WriteToImmediateWindow --> End
```

### 3.2 日志初始化流程

```mermaid
sequenceDiagram
    participant Caller as 调用模块
    participant Debug as modDebug
    participant Config as 配置系统
    participant File as 日志文件

    Caller->>Debug: InitDebug(level, enableDebug, enableFileLogging, enableImmediateOutput, logFilePath)
    Debug->>Config: 获取配置值(如果参数未指定)
    Config-->>Debug: 返回配置值
    Debug->>Debug: 设置IsDebugEnabled

    alt IsDebugEnabled = True
        Debug->>Debug: 设置DebugLevel
        Debug->>Debug: 设置IsFileLoggingEnabled
        Debug->>Debug: 设置IsImmediateOutputEnabled
        Debug->>Config: 获取UTF8_ENCODING配置

        alt IsFileLoggingEnabled = True
            Debug->>Debug: 确定日志文件路径
            Debug->>Debug: 验证路径有效性
            Debug->>File: 初始化日志文件
            Debug->>File: 写入日志文件头
        end

        Debug->>Debug: 记录初始化信息
    end

    Debug-->>Caller: 初始化完成
```

## 4. 函数说明

### 4.1 公共常量

| 常量名 | 类型 | 值 | 说明 |
|--------|------|-----|------|
| DEBUG_LEVEL_ERROR | Integer | 1 | 仅记录错误 |
| DEBUG_LEVEL_WARNING | Integer | 2 | 记录错误和警告 |
| DEBUG_LEVEL_INFO | Integer | 3 | 记录错误、警告和信息 |
| DEBUG_LEVEL_VERBOSE | Integer | 4 | 记录所有信息，包括详细跟踪 |

### 4.2 公共变量

| 变量名 | 类型 | 说明 |
|--------|------|------|
| IsDebugEnabled | Boolean | 是否开启debug模式 |
| DebugLevel | Integer | 当前调试级别 |
| IsFileLoggingEnabled | Boolean | 是否启用文件日志 |
| IsImmediateOutputEnabled | Boolean | 是否输出到即时窗口 |

### 4.3 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| InitDebug | 无 | Optional level As Integer = -1, Optional enableDebug As Boolean = True, Optional enableFileLogging As Boolean = True, Optional enableImmediateOutput As Boolean = False, Optional logFilePath As String = "" | 初始化调试模块，设置调试级别、是否启用调试、是否启用文件日志、是否输出到即时窗口和日志文件路径 |
| LogError | 无 | errNumber As Long, errDescription As String, errSource As String | 记录错误信息 |
| LogWarning | 无 | message As String, source As String | 记录警告信息 |
| LogInfo | 无 | message As String, source As String | 记录一般信息 |
| LogVerbose | 无 | message As String, source As String | 记录详细跟踪信息 |
| LogFunctionEntry | 无 | functionName As String, params As String | 记录函数进入 |
| LogFunctionExit | 无 | functionName As String, result As String | 记录函数退出 |

| OpenLogFile | 无 | 无 | 打开日志文件 |
| ReinitializeLog | 无 | 无 | 重新初始化日志文件 |
| CloseLogFile | 无 | 无 | 关闭日志文件 |

### 4.4 私有函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| WriteToLog | 无 | logType As String, message As String, source As String | 将日志写入文件和立即窗口 |
| PadRight | String | str As String, length As Integer | 将字符串右填充到指定长度 |
| GetDebugLevelName | String | level As Integer | 获取调试级别名称 |

## 5. 日志格式

### 5.1 文件日志格式

日志文件的格式如下：

```
====================================================
  Project Management Gantt Chart System - Debug Log
  Start time: yyyy-mm-dd hh:mm:ss
  Debug level: LEVEL
====================================================

Time              | Type   | Source                    | Message
------------------------------------------------------------
yyyy-mm-dd hh:mm:ss | ERROR  | modMain.SomeFunction     | Error message
yyyy-mm-dd hh:mm:ss | INFO   | modData.AnotherFunction  | Info message
...

====================================================
  End time: yyyy-mm-dd hh:mm:ss
====================================================
```

## 6. 使用示例

### 6.1 初始化调试模块

```vba
' 初始化调试模块，设置调试级别为详细模式，启用调试，启用文件日志，禁用即时窗口输出
modDebug.InitDebug level:=modDebug.DEBUG_LEVEL_VERBOSE, enableDebug:=True, _
                   enableFileLogging:=True, enableImmediateOutput:=False

' 使用配置值初始化调试模块
Dim debugLevel As Integer
Dim enableDebug As Boolean
Dim enableFileLogging As Boolean
Dim enableImmediateOutput As Boolean

' 获取配置值
debugLevel = CInt(GetConfig("DebugLevel", 4))
enableDebug = CBool(GetConfig("EnableDebug", False))
enableFileLogging = CBool(GetConfig("EnableFileLogging", False))
enableImmediateOutput = CBool(GetConfig("EnableImmediateOutput", False))

' 初始化调试系统，使用配置值
modDebug.InitDebug _
    level:=debugLevel, _
    enableDebug:=enableDebug, _
    enableFileLogging:=enableFileLogging, _
    enableImmediateOutput:=enableImmediateOutput
```

### 6.2 记录不同级别的日志

```vba
' 记录错误信息
modDebug.LogError Err.Number, Err.Description, "modMain.SomeFunction"

' 记录警告信息
modDebug.LogWarning "配置项未找到，使用默认值", "modData.GetConfigValue"

' 记录一般信息
modDebug.LogInfo "开始生成甘特图", "modMain.GenerateGanttChart"

' 记录详细跟踪信息
modDebug.LogVerbose "处理第 " & i & " 行数据", "modData.ProcessData"
```

### 6.3 记录函数进入和退出

```vba
Public Function SomeFunction(param1 As String, param2 As Integer) As Boolean
    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.SomeFunction", "param1=" & param1 & ", param2=" & param2

    ' 函数主体代码
    ' ...

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.SomeFunction", "result=" & result
    SomeFunction = result
End Function
```

## 7. 依赖关系

modDebug模块的依赖关系：

```mermaid
flowchart TD
    modDebug --> Excel[Excel对象模型]
    modDebug --> VBA[VBA标准库]
    modDebug --> FileSystem[文件系统]
    modDebug --> modData[modData模块]
    modDebug --> ADODB[ADODB.Stream]
    modData --> Config[配置系统]
```
