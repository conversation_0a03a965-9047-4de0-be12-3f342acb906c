# 甘特图生成逻辑说明文档

本文档详细说明Excel VBA项目管理甘特图系统中里程碑和任务图的生成逻辑，包括输入、输出和处理过程。

## 1. 总体流程

甘特图生成由`CreateGanttChart`函数触发，主要包含以下步骤：

1. 获取项目信息和任务数据
2. 创建时间轴（包含年份、月份和周数）
3. 绘制任务和里程碑

```mermaid
graph TD
    A[开始: CreateGanttChart] --> B[获取项目信息和任务数据]
    B --> C[创建时间轴]
    C --> D[绘制任务和里程碑]
    D --> E[结束]
```

## 2. 输入数据

### 2.1 项目信息

从ProjectInfo工作表获取的项目基本信息：
- 项目名称（ProjectName）
- 项目经理（ProjectManager）
- 项目开始日期（StartDate）
- 项目结束日期（EndDate）

### 2.2 任务数据

从Milestones&WBS工作表获取的任务集合，每个任务包含以下属性：
- ID：任务唯一标识符
- Description：任务描述
- Type：任务类型（"A"=活动任务，"M"=里程碑）
- StartDate：开始日期
- EndDate：结束日期（里程碑的EndDate等于StartDate）
- Progress：完成进度（0-1之间的小数）
- Color：任务颜色（十六进制格式，如"#FF0000"）
- Position：位置属性（"next"、"same"、数字）
- Category：类别信息（可选）
- TextPosition：标签位置（"right"、"left"、"top"、"bottom"、"inside"）
- Baseline：基准线日期（可选）

## 3. 时间轴生成

### 3.1 CreateTimeline函数

```vba
Private Sub CreateTimeline(startDate As Date, endDate As Date)
```

#### 输入
- startDate：项目开始日期
- endDate：项目结束日期

#### 处理过程
1. 设置工作表格式（白色背景、居中对齐）
2. 从C列（第3列）开始生成时间轴
3. 生成年份行（第3行）
4. 生成月份行（第4行）
5. 生成周数行（第5行，使用ISO 8601标准）
6. 合并相同年份和月份的单元格
7. 合并项目信息单元格（B1:B5）

#### 输出
在GanttChart工作表上创建时间轴，包括：
- 年份行（第3行）
- 月份行（第4行）
- 周数行（第5行）

### 3.2 坐标系统建立

```vba
Private Function EstablishTimelineCoordinateSystem(ws As Worksheet, projectInfo As Dictionary) As Dictionary
```

#### 输入
- ws：甘特图工作表引用
- projectInfo：项目信息字典

#### 处理过程
1. 计算时间轴起始日期（项目开始日期所在月份的第一天）
2. 计算时间轴结束日期（项目结束日期所在月份的最后一天）
3. 确定时间轴的起始列（C列）和结束列
4. 计算坐标系原点（C5单元格的左下角）
5. 计算坐标系宽度（从原点到结束列的右边缘）
6. 计算总天数（从起始日期到结束日期）

#### 输出
返回包含以下信息的坐标系字典：
- StartDate：坐标系起始日期
- EndDate：坐标系结束日期
- OriginX：坐标系原点X坐标
- Width：坐标系宽度
- TotalDays：总天数

## 4. 任务和里程碑绘制

### 4.1 DrawTasksAndMilestones函数

```vba
Private Sub DrawTasksAndMilestones(tasks As Collection)
```

#### 输入
- tasks：任务集合

#### 处理过程
1. 获取项目信息
2. 建立时间轴坐标系（只创建一次）
3. 初始化行位置（从第6行开始）
4. 初始化基准线收集变量
5. 遍历每个任务：
   - 确定任务行位置并处理类别信息
   - 根据任务类型绘制任务或里程碑
   - 收集基准线信息
6. 统一处理所有基准线

#### 输出
在甘特图工作表上绘制所有任务和里程碑，包括：
- 任务条（矩形）
- 里程碑（菱形）
- 进度条
- 任务和里程碑描述标签
- 基准线（垂直虚线）

### 4.2 任务行位置和类别处理

```vba
Private Function DetermineTaskRowAndCategory(task As Dictionary, ws As Worksheet, currentRow As Long, lastTaskRow As Long, taskIndex As Long, ByRef currentCategory As String, ByRef isCurrentRowCategorySet As Boolean) As Long
```

#### 输入
- task：当前处理的任务
- ws：工作表引用
- currentRow：当前处理行
- lastTaskRow：上一个任务的行位置
- taskIndex：任务索引（从1开始）
- currentCategory：当前类别（引用传递）
- isCurrentRowCategorySet：当前行的类别是否已设置（引用传递）

#### 处理过程
1. 获取任务的Position和Category属性
2. 根据taskIndex分为两种处理逻辑：
   - 第一个任务（taskIndex = 1）：
     - 直接使用currentRow作为结果行
     - 如果有类别，设置currentCategory并标记isCurrentRowCategorySet为True
   - 后续任务（taskIndex > 1）：
     - 根据Position属性确定行位置：
       - "same"或0：与上一任务同行
       - "next"：移动到下一行
       - 数字：移动指定的行数
     - 如果是新行，重置isCurrentRowCategorySet为False
     - 处理类别继承和类别标题行：
       - 如果任务有非空类别且与当前类别不同，更新currentCategory
       - 如果是新行且任务有非空类别且当前行类别未设置，添加类别标题

#### 输出
- 返回任务应该放置的行号
- 更新currentCategory和isCurrentRowCategorySet引用参数
- 在新类别的行上添加类别标题

### 4.3 任务绘制

```vba
Private Sub DrawTask(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

#### 输入
- task：任务信息字典
- row：任务行位置
- timelineCoords：时间轴坐标系信息

#### 处理过程
1. 计算任务条的位置和大小：
   - 使用CalculateXCoordinate函数将任务的开始日期和结束日期转换为X坐标
   - 计算任务条的左边缘、宽度和高度
   - 计算任务条的垂直居中位置
2. 创建任务条形状（矩形）
3. 设置任务条的颜色和边框
4. 如果任务有进度，添加进度条
5. 添加任务描述标签
6. 动态调整行高以适应任务和标签

#### 输出
在指定行位置创建：
- 任务条形状（矩形）
- 进度条形状（如果有进度）
- 任务描述标签

### 4.4 里程碑绘制

```vba
Private Sub DrawMilestone(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

#### 输入
- task：里程碑信息字典
- row：里程碑行位置
- timelineCoords：时间轴坐标系信息

#### 处理过程
1. 计算里程碑在坐标系中的精确位置：
   - 使用CalculateXCoordinate函数将里程碑日期转换为X坐标
   - 计算Y坐标，使里程碑在行内垂直居中
2. 创建里程碑形状（菱形）
3. 设置里程碑的颜色和边框
4. 添加里程碑描述标签
5. 动态调整行高以适应里程碑和标签

#### 输出
在指定行位置创建：
- 里程碑形状（菱形）
- 里程碑描述标签

### 4.5 日期到坐标的转换

```vba
Private Function CalculateXCoordinate(targetDate As Date, coords As Dictionary) As Double
```

#### 输入
- targetDate：目标日期
- coords：坐标系信息字典

#### 处理过程
1. 计算目标日期与起始日期的天数差（并加1进行修正）
2. 计算这个天数差在总天数中的比例
3. 根据这个比例计算在总宽度中的偏移量
4. 将原点坐标和偏移量相加，得到最终的X坐标

#### 输出
返回目标日期对应的X坐标（像素值）

### 4.6 标签添加

```vba
Private Function AddTaskLabelWithCoordinates2(task As Dictionary, shape As Shape, centerX As Double, centerY As Double, shapeSize As Double, labelDistance As Long) As Shape
```

#### 输入
- task：任务或里程碑信息
- shape：任务条或里程碑形状
- centerX：形状中心X坐标
- centerY：形状中心Y坐标
- shapeSize：形状大小
- labelDistance：标签与形状的距离

#### 处理过程
1. 创建临时文本框以计算文本尺寸
2. 根据任务的TextPosition属性或自动判断确定标签位置：
   - "inside"：放在形状内部，居中对齐
   - "right"：放在形状右侧，左对齐
   - "left"：放在形状左侧，右对齐
   - "top"：放在形状上方，左对齐
   - "bottom"：放在形状下方，左对齐
3. 创建标签文本框并设置文本内容和格式
4. 根据位置调整标签对齐方式

#### 输出
返回创建的标签形状（文本框）

## 5. 基准线处理

### 5.1 基准线收集

在DrawTasksAndMilestones函数中，基准线信息在遍历任务时同时收集：

```vba
' 检查并收集基准线信息
If task.Exists("Baseline") And Not IsEmpty(task("Baseline")) Then
    baselineDateStr = task("Baseline")
    If IsDate(baselineDateStr) Then
        baselineDate = CDate(baselineDateStr)
        ' 使用字典避免重复处理相同日期的基准线
        If Not baselineDates.Exists(baselineDateStr) Then
            ' 创建基准线信息字典
            Set baselineInfo = New Dictionary
            baselineInfo.Add "Date", baselineDate
            baselineInfo.Add "Description", IIf(task.Exists("BaselineDescription"), task("BaselineDescription"), "")
            ' 添加到基准线集合
            baselineCollection.Add baselineInfo
            ' 标记该日期已处理
            baselineDates.Add baselineDateStr, True
        End If
    End If
End If
```

### 5.2 基准线绘制

在所有任务处理完成后，统一处理所有基准线：

```vba
' 绘制所有基准线（在所有任务处理完成后）
If baselineCollection.Count > 0 Then
    modDebug.LogInfo "开始绘制基准线，总数: " & baselineCollection.Count, "modGantt.DrawTasksAndMilestones"
    For i = 1 To baselineCollection.Count
        Set baselineInfo = baselineCollection(i)
        DrawBaseline baselineInfo("Date"), ws, timelineCoords
    Next i
    modDebug.LogInfo "基准线绘制完成", "modGantt.DrawTasksAndMilestones"
End If
```

## 6. 关键算法

### 6.1 日期到坐标的映射算法

日期到X坐标的映射使用线性插值算法：

```vba
xCoord = coords("OriginX") + (coords("Width") * daysDiff / totalDaysAdjusted)
```

其中：
- coords("OriginX")：坐标系原点X坐标
- coords("Width")：坐标系总宽度
- daysDiff：目标日期与起始日期的天数差（加1修正）
- totalDaysAdjusted：坐标系覆盖的总天数（加1修正）

### 6.2 任务位置计算算法

任务位置计算基于Position属性和当前行位置：

```vba
' 根据Position属性确定行位置
If taskPosition = "same" Or taskPosition = 0 Then
    ' 与上一任务同行
    resultRow = lastTaskRow
ElseIf taskPosition = "next" Then
    ' 移动到下一行
    resultRow = currentRow + 1
ElseIf IsNumeric(taskPosition) Then
    ' 移动指定的行数
    resultRow = currentRow + CInt(taskPosition)
Else
    ' 默认移动到下一行
    resultRow = currentRow + 1
End If
```

### 6.3 类别处理算法

类别处理算法确保类别标题正确显示：

```vba
' 如果是新行或首个任务，重置行类别设置状态
If resultRow <> currentRow Or taskIndex = 1 Then
    isCurrentRowCategorySet = False
End If

' 如果当前行的类别尚未设置且任务有非空类别
If Not isCurrentRowCategorySet And task.Exists("Category") And task("Category") <> "" Then
    ' 设置当前类别
    currentCategory = task("Category")
    isCurrentRowCategorySet = True
    
    ' 如果是新行或首个任务，添加类别行标题
    If resultRow <> currentRow Or taskIndex = 1 Then
        ' 在当前行添加类别标题
        ws.Cells(resultRow, 2).Value = currentCategory
        ws.Cells(resultRow, 2).Font.Bold = True
        ws.Cells(resultRow, 2).Font.Size = 11
        ws.Cells(resultRow, 2).HorizontalAlignment = xlLeft
        ws.Cells(resultRow, 2).VerticalAlignment = xlCenter
    End If
End If
```

## 7. 总结

甘特图生成系统通过以下关键步骤实现任务和里程碑的可视化：

1. 建立基于项目日期的时间轴
2. 创建精确的坐标系统，将日期映射到X坐标
3. 根据任务的Position和Category属性确定行位置
4. 根据任务类型绘制任务条或里程碑
5. 添加描述标签和进度条
6. 绘制基准线

这种实现方式具有以下优势：
- 精确的日期到坐标的映射
- 灵活的任务位置控制
- 支持类别分组和层次结构
- 动态调整行高以适应内容
- 可配置的视觉样式
