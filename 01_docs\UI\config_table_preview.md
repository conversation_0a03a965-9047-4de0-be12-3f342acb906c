# 配置表视觉预览功能

## 1. 功能概述

配置表视觉预览功能为`configTable`超级表中的特定类别（Font和Color）的配置项提供直观的视觉效果预览：

- 对于Category为"Font"的项，在Description单元格中应用相应的字体、大小、粗体和颜色属性
- 对于Category为"Color"的项，在Description单元格中应用相应的颜色填充，并根据颜色亮度自动调整文本颜色

这样用户可以直观地看到配置项的实际效果，而不需要想象或者在其他地方测试。

## 2. 使用方法

有三种方式可以应用配置表预览：

### 2.1 通过Ribbon按钮

1. 点击Ribbon上的"甘特图"选项卡
2. 在"配置工具"组中，点击"应用配置预览"按钮
3. 系统会自动切换到"Config"工作表并应用预览效果

### 2.2 通过宏列表

1. 按下Alt+F8打开宏列表
2. 选择"ApplyConfigTablePreviewMacro"
3. 点击"运行"
4. 系统会自动切换到"Config"工作表并应用预览效果

### 2.3 通过VBA代码

在VBA代码中，可以直接调用以下函数：

```vba
modUI.ApplyConfigTablePreview
```

## 3. 预览效果说明

### 3.1 字体预览

对于Category为"Font"的配置项，根据ConfigName的后缀确定应用哪种字体属性：

- 以"Font"结尾的配置项（如ProjectNameFont）：应用字体名称
- 以"FontSize"结尾的配置项（如ProjectNameFontSize）：应用字体大小
- 以"FontBold"结尾的配置项（如ProjectNameFontBold）：应用粗体设置
- 以"FontColor"结尾的配置项（如ProjectNameFontColor）：应用字体颜色

每种属性的预览效果：
- 字体名称：Description单元格会使用指定字体显示，并添加"示例: Arial"文本
- 字体大小：Description单元格会使用指定大小显示，并添加"示例: 16磅"文本
- 粗体设置：Description单元格会应用粗体或常规设置，并添加"示例: 粗体"或"示例: 常规"文本
- 字体颜色：Description单元格会应用指定颜色，并添加"示例: #000000"文本

### 3.2 颜色预览

对于Category为"Color"的配置项：

- 首先检查ConfigValue是否非空且是有效的十六进制颜色代码（#RRGGBB格式）
- 如果不是有效的颜色代码，直接提取ConfigValue单元格的Interior.Color，并自动更新ConfigValue为对应的十六进制颜色代码
- Description单元格会应用配置的颜色作为背景色
- 根据颜色的亮度自动调整文本颜色（深色背景使用白色文本，浅色背景使用黑色文本）

示例：
- 配置项：`ChartBackgroundColor = #FFFFFF`
- 预览效果：Description单元格会有白色背景，文本为黑色
- 配置项：`CurrentDateLineColor = #FF0000`
- 预览效果：Description单元格会有红色背景，文本为白色
- 配置项：`ChartGridlineColor = 灰色`（非有效颜色代码）
- 处理：直接提取ConfigValue单元格的Interior.Color，假设为灰色，自动更新为`#DDDDDD`，然后应用预览效果
- 配置项：`ChartAlternateRowColor = `（空值）
- 处理：直接提取ConfigValue单元格的Interior.Color，假设为浅灰色，自动更新为`#F5F5F5`，然后应用预览效果
- 配置项：`ChartBackgroundColor = 白色`（非有效颜色代码且单元格无背景色）
- 处理：使用默认颜色（蓝色 #3366CC），更新ConfigValue单元格值，然后应用预览效果

## 4. 实现架构

### 4.1 模块组织

功能实现分布在两个主要模块中：
- `modUI.bas`：包含核心预览功能的实现
- `modRibbon.bas`：包含Ribbon界面集成

### 4.2 函数层次结构

```
modUI.bas
├── ApplyConfigTablePreview (主函数)
│   ├── ApplyFontPreview
│   │   ├── IsFontInstalled
│   │   └── GetRGBColor (来自modUtilities)
│   └── ApplyColorPreview
│       ├── GetRGBColor (来自modUtilities)
│       └── IsColorDark
└── ApplyConfigTablePreviewMacro (宏入口)

modRibbon.bas
└── Ribbon_ApplyConfigPreview (Ribbon回调)
    └── modUI.ApplyConfigTablePreview
```

## 5. 调用逻辑详解

### 5.1 用户入口点

用户可以通过三种方式调用此功能：

1. **Ribbon按钮**
   ```
   用户点击 → Ribbon_ApplyConfigPreview → ApplyConfigTablePreview
   ```

2. **宏列表**
   ```
   用户运行宏 → ApplyConfigTablePreviewMacro → ApplyConfigTablePreview
   ```

3. **VBA代码**
   ```
   直接调用 → modUI.ApplyConfigTablePreview
   ```

### 5.2 主函数流程 (ApplyConfigTablePreview)

```
1. 获取Config工作表引用
2. 获取configTable引用
3. 获取必要的列索引（Category、Description、ConfigValue）
4. 清除所有单元格格式，恢复默认状态
5. 遍历每一行数据
   5.1 获取Category和ConfigValue
   5.2 根据Category类型分发处理
      - 如果是"FONT"，调用ApplyFontPreview
      - 如果是"COLOR"，调用ApplyColorPreview
```

### 5.3 字体预览流程 (ApplyFontPreview)

```
1. 获取单元格所在行的数据和ConfigName
2. 根据ConfigName的后缀确定应用哪种字体属性：
   - 以"Font"结尾：应用字体名称
   - 以"FontSize"结尾：应用字体大小
   - 以"FontBold"结尾：应用粗体设置
   - 以"FontColor"结尾：应用字体颜色
3. 根据属性类型应用相应的格式
4. 更新单元格文本，添加相应的示例信息
```

字体预览流程根据ConfigName的后缀来确定应用哪种字体属性，每种属性都有独立的处理逻辑。

### 5.4 颜色预览流程 (ApplyColorPreview)

```
1. 获取ConfigValue单元格引用
2. 检查ConfigValue是否非空且是有效的十六进制颜色代码（#RRGGBB格式）
3. 如果不是有效的颜色代码：
   3.1 检查ConfigValue单元格是否有背景色
   3.2 如果有背景色，直接获取Interior.Color值
   3.3 使用简单的十六进制转换：`Right("000000" & Hex(rgbColor), 6)`
   3.4 在更新ConfigValue单元格值时添加"#"前缀
   3.5 更新ConfigValue单元格的值为十六进制颜色代码
   3.6 如果单元格没有背景色，使用默认颜色（蓝色 #3366CC）并更新ConfigValue单元格
4. 转换颜色代码为RGB值
5. 应用颜色填充到Description单元格背景
6. 计算颜色亮度
7. 根据亮度自动调整文本颜色
   - 深色背景使用白色文本
   - 浅色背景使用黑色文本
```

颜色处理的关键点：

1. **Excel颜色提取方式**：
   ```
   从Excel单元格提取颜色的简单方法：
   hexColor = Right("000000" & Hex(rng.Interior.Color), 6)

   这种方法直接将Interior.Color值转换为十六进制，无需手动分解RGB分量
   在需要使用时再添加"#"前缀：configValueCell.Value = "#" & hexColor
   ```

2. **颜色亮度计算公式**：
   ```
   亮度 = 0.299*R + 0.587*G + 0.114*B
   亮度 < 128 视为深色
   ```

### 5.5 辅助函数

1. **IsFontInstalled**
   ```
   1. 保存当前Normal样式的字体
   2. 尝试将Normal样式的字体设置为目标字体
   3. 检查设置是否成功
   4. 恢复原始字体
   ```

2. **IsColorDark**
   ```
   1. 提取RGB分量
   2. 计算感知亮度
   3. 判断亮度是否小于128
   ```

## 6. 错误处理机制

整个实现中包含多层错误处理：

1. **工作表和表格检查**
   ```vba
   ' 直接切换到Config工作表
   On Error Resume Next
   ThisWorkbook.Worksheets("Config").Activate
   If Err.Number <> 0 Then
       MsgBox "未找到Config工作表，无法应用配置预览。", vbExclamation, "错误"
       modDebug.LogError Err.Number, "未找到Config工作表", "modUI.ApplyConfigTablePreviewMacro"
       Exit Sub
   End If
   On Error GoTo ErrorHandler
   ```

2. **列索引检查**
   ```vba
   If categoryColIndex = 0 Or descriptionColIndex = 0 Or configValueColIndex = 0 Then
       modDebug.LogWarning "未找到必要的列", "modUI.ApplyConfigTablePreview"
       Exit Sub
   End If
   ```

3. **字体存在性检查**
   ```vba
   If IsFontInstalled(configValue) Then
       ' 应用字体...
   End If
   ```

4. **颜色格式检查**
   ```vba
   If Left(configValue, 1) = "#" And Len(configValue) = 7 Then
       ' 应用颜色...
   End If
   ```

5. **全局错误处理**
   ```vba
   On Error GoTo ErrorHandler
   ' ...
   ErrorHandler:
       modDebug.LogError Err.Number, Err.Description, "modUI.ApplyConfigTablePreview"
   ```

## 7. 性能优化考虑

1. **批量清除格式**
   ```vba
   tbl.DataBodyRange.Cells.Font.Name = "Arial"
   tbl.DataBodyRange.Cells.Font.Size = 10
   ' ...其他批量设置
   ```

2. **提前获取列索引**
   ```vba
   categoryColIndex = tbl.ListColumns("Category").Index
   descriptionColIndex = tbl.ListColumns("Description").Index
   configValueColIndex = tbl.ListColumns("ConfigValue").Index
   ```

3. **条件过滤**
   ```vba
   Select Case UCase(category)
       Case "FONT"
           ' 应用字体预览
       Case "COLOR"
           ' 应用颜色预览
   End Select
   ```

4. **关联属性查找优化**
   ```vba
   ' 如果是字体名称配置，查找相关的字体大小配置
   If InStr(1, configName, "Font") > 0 And InStr(1, configName, "Size") = 0 And InStr(1, configName, "Bold") = 0 And InStr(1, configName, "Color") = 0 Then
       ' 查找相关配置...
   End If
   ```

## 8. 注意事项

1. 预览效果只是临时的，不会保存到工作簿中。每次打开工作簿或切换工作表后，需要重新应用预览效果。
2. 预览功能不会修改配置项的实际值，只是改变Description单元格的显示效果。
3. 如果配置值格式不正确（如颜色代码不是#RRGGBB格式），则不会应用预览效果。
4. 应用预览效果前，会先清除所有单元格的格式，恢复到默认状态。
5. 系统会自动切换到"Config"工作表，无需手动切换。

## 9. 扩展建议

未来可以考虑扩展该功能，支持更多类型的配置项预览：

1. 对于数值类型的配置项（如字体大小、行高等），可以使用条形图或数值指示器
2. 对于布尔类型的配置项，可以使用复选框或开关图标
3. 对于枚举类型的配置项，可以使用下拉列表或图标集合
4. 添加实时预览功能，当用户修改配置值时自动更新预览效果
