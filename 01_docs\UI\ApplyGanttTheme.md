# ApplyGanttTheme 函数说明文档

## 功能概述

`ApplyGanttTheme` 函数用于在甘特图生成后应用主题设置，包括背景颜色、交替行颜色、网格线、字体、边框等视觉元素。该函数提供了多种预设主题，同时也支持通过配置项进行自定义设置，使甘特图的外观更加美观和专业。函数将甘特图工作表划分为多个细分区域，并为每个区域应用相应的样式设置。

## 函数定义

```vba
Public Sub ApplyGanttTheme(ws As Worksheet)
```

## 参数说明

| 参数名 | 数据类型 | 说明 |
| ------ | -------- | ---- |
| ws | Worksheet | 要应用主题的工作表对象，通常是甘特图工作表 |

## 配置项

`ApplyGanttTheme` 函数使用以下配置项来控制主题设置：

### 全局配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| ChartTheme | 1 | 甘特图主题编号：1-5为预设主题，custom为自定义主题（使用配置表中的自定义配置项）。默认使用主题1 |
| ChartGridlinesType | all | 网格线类型，可选值：all/horizontal/vertical |
| ChartGridlineColor | #DDDDDD | 网格线颜色（十六进制颜色代码） |
| ChartBackgroundColor | #FFFFFF | 甘特图背景颜色（十六进制颜色代码） |
| ChartAlternateRowColor | #F5F5F5 | 甘特图交替行颜色（十六进制颜色代码） |
| ColumnAWidth | 5 | A列宽度 |

### 项目名称区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| ProjectNameFont | Barlow | 项目名称字体 |
| ProjectNameFontSize | 14 | 项目名称字号 |
| ProjectNameFontBold | True | 项目名称是否粗体 |
| ProjectNameFontColor | #000000 | 项目名称字体颜色 |
| ProjectNameBackColor | #FFFFFF | 项目名称背景颜色 |
| ProjectNameBorderStyle | none | 项目名称边框样式，可选值：none/all/outline/bottom |
| ProjectNameRowHeight | auto | 项目名称行高，auto表示自动调整，或者指定数值 |
| ProjectNameTextAlign | left | 项目名称文本对齐方式，可选值：left/center/right |

### 项目经理名称区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| ProjectManagerFont | Barlow | 项目经理名称字体 |
| ProjectManagerFontSize | 11 | 项目经理名称字号 |
| ProjectManagerFontBold | False | 项目经理名称是否粗体 |
| ProjectManagerFontColor | #333333 | 项目经理名称字体颜色 |
| ProjectManagerBackColor | #FFFFFF | 项目经理名称背景颜色 |
| ProjectManagerBorderStyle | none | 项目经理名称边框样式，可选值：none/all/outline/bottom |
| ProjectManagerRowHeight | auto | 项目经理名称行高，auto表示自动调整，或者指定数值 |
| ProjectManagerTextAlign | left | 项目经理名称文本对齐方式，可选值：left/center/right |

### 补充信息区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| SupplementInfoFont | Barlow | 补充信息字体 |
| SupplementInfoFontSize | 10 | 补充信息字号 |
| SupplementInfoFontBold | False | 补充信息是否粗体 |
| SupplementInfoFontColor | #333333 | 补充信息字体颜色 |
| SupplementInfoBackColor | #FFFFFF | 补充信息背景颜色 |
| SupplementInfoBorderStyle | none | 补充信息边框样式，可选值：none/all/outline/bottom |
| SupplementInfoRowHeight | auto | 补充信息行高，auto表示自动调整，或者指定数值 |

### 时间轴表头年区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| TimelineYearFont | Barlow | 时间轴年字体 |
| TimelineYearFontSize | 11 | 时间轴年字号 |
| TimelineYearFontBold | True | 时间轴年是否粗体 |
| TimelineYearFontColor | #FFFFFF | 时间轴年字体颜色 |
| TimelineYearBackColor | #4472C4 | 时间轴年背景颜色 |
| TimelineYearBorderStyle | all | 时间轴年边框样式，可选值：none/all/outline/bottom |
| TimelineYearRowHeight | auto | 时间轴年行高，auto表示自动调整，或者指定数值 |

### 时间轴表头月区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| TimelineMonthFont | Barlow | 时间轴月字体 |
| TimelineMonthFontSize | 10 | 时间轴月字号 |
| TimelineMonthFontBold | False | 时间轴月是否粗体 |
| TimelineMonthFontColor | #FFFFFF | 时间轴月字体颜色 |
| TimelineMonthBackColor | #5B9BD5 | 时间轴月背景颜色 |
| TimelineMonthBorderStyle | all | 时间轴月边框样式，可选值：none/all/outline/bottom |
| TimelineMonthRowHeight | auto | 时间轴月行高，auto表示自动调整，或者指定数值 |

### 时间轴表头周区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| TimelineWeekFont | Barlow | 时间轴周字体 |
| TimelineWeekFontSize | 9 | 时间轴周字号 |
| TimelineWeekFontBold | False | 时间轴周是否粗体 |
| TimelineWeekFontColor | #FFFFFF | 时间轴周字体颜色 |
| TimelineWeekBackColor | #70AD47 | 时间轴周背景颜色 |
| TimelineWeekBorderStyle | all | 时间轴周边框样式，可选值：none/all/outline/bottom |
| TimelineWeekRowHeight | auto | 时间轴周行高，auto表示自动调整，或者指定数值 |

### 甘特图任务及里程碑绘图区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| ChartGridlinesArea | all | 网格线应用区域，可选值：all/header/ganttDrawing/taskCategory/header,ganttDrawing/header,taskCategory/ganttDrawing,taskCategory |

### 任务类区域配置

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| CategoryFont | Barlow | 任务类别字体 |
| CategoryFontSize | 11 | 任务类别字号 |
| CategoryFontBold | True | 任务类别是否粗体 |
| CategoryFontColor | #000000 | 任务类别字体颜色 |
| CategoryBackColor | #F2F2F2 | 任务类别背景颜色 |
| CategoryBorderStyle | outline | 任务类别边框样式，可选值：none/all/outline/bottom |
| CategoryColumnWidth | 30 | 任务类别列宽 |
| CategoryWrapText | True | 任务类别是否自动换行 |
| CategoryTextAlign | center | 任务类别文本对齐方式，可选值：left/center/right |

## 预设主题

函数提供了5种预设主题，每种主题都有不同的视觉风格。每个主题都会覆盖所有区域的配置项：

### 主题1：经典浅色主题

**全局设置**
- 背景颜色：白色 (#FFFFFF)
- 交替行颜色：浅灰色 (#F5F5F5)
- 网格线颜色：浅灰色 (#DDDDDD)
- 网格线区域：所有区域
- 网格线类型：水平和垂直
- 字体：Barlow
- 字号：10
- A列宽度：5

**项目名称区域**
- 字体：Barlow
- 字号：14
- 粗体：是
- 字体颜色：黑色 (#000000)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：无
- 行高：自动

**项目经理名称区域**
- 字体：Barlow
- 字号：11
- 粗体：否
- 字体颜色：深灰色 (#333333)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：无
- 行高：自动

**补充信息区域**
- 字体：Barlow
- 字号：10
- 粗体：否
- 字体颜色：深灰色 (#333333)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：无
- 行高：自动

**时间轴表头年区域**
- 字体：Barlow
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：蓝色 (#4472C4)
- 边框样式：全部
- 行高：自动

**时间轴表头月区域**
- 字体：Barlow
- 字号：10
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：浅蓝色 (#5B9BD5)
- 边框样式：全部
- 行高：自动

**时间轴表头周区域**
- 字体：Barlow
- 字号：9
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：绿色 (#70AD47)
- 边框样式：全部
- 行高：自动

**任务类区域**
- 字体：Barlow
- 字号：11
- 粗体：是
- 字体颜色：黑色 (#000000)
- 背景颜色：浅灰色 (#F2F2F2)
- 边框样式：轮廓
- 列宽：30
- 文本对齐：居中

### 主题2：深色主题

**全局设置**
- 背景颜色：深灰色 (#333333)
- 交替行颜色：中灰色 (#444444)
- 网格线颜色：灰色 (#555555)
- 网格线区域：所有区域
- 网格线类型：水平和垂直
- 字体：Barlow
- 字号：10
- A列宽度：5

**项目名称区域**
- 字体：Barlow
- 字号：14
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深灰色 (#333333)
- 边框样式：无
- 行高：自动

**项目经理名称区域**
- 字体：Barlow
- 字号：11
- 粗体：否
- 字体颜色：浅灰色 (#CCCCCC)
- 背景颜色：深灰色 (#333333)
- 边框样式：无
- 行高：自动

**补充信息区域**
- 字体：Barlow
- 字号：10
- 粗体：否
- 字体颜色：浅灰色 (#CCCCCC)
- 背景颜色：深灰色 (#333333)
- 边框样式：无
- 行高：自动

**时间轴表头年区域**
- 字体：Barlow
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深蓝色 (#2F5597)
- 边框样式：全部
- 行高：自动

**时间轴表头月区域**
- 字体：Barlow
- 字号：10
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：蓝色 (#4472C4)
- 边框样式：全部
- 行高：自动

**时间轴表头周区域**
- 字体：Barlow
- 字号：9
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深绿色 (#548235)
- 边框样式：全部
- 行高：自动

**任务类区域**
- 字体：Barlow
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深灰色 (#3A3A3A)
- 边框样式：轮廓
- 列宽：30
- 文本对齐：居中

### 主题3：蓝色主题

**全局设置**
- 背景颜色：浅蓝色 (#F0F8FF)
- 交替行颜色：更浅的蓝色 (#E6F2FF)
- 网格线颜色：淡蓝色 (#B0C4DE)
- 网格线区域：所有区域
- 网格线类型：仅水平
- 字体：Calibri
- 字号：10
- A列宽度：5

**项目名称区域**
- 字体：Calibri
- 字号：14
- 粗体：是
- 字体颜色：深蓝色 (#1F4E79)
- 背景颜色：浅蓝色 (#F0F8FF)
- 边框样式：底部
- 行高：自动

**项目经理名称区域**
- 字体：Calibri
- 字号：11
- 粗体：否
- 字体颜色：蓝色 (#2F5597)
- 背景颜色：浅蓝色 (#F0F8FF)
- 边框样式：无
- 行高：自动

**补充信息区域**
- 字体：Calibri
- 字号：10
- 粗体：否
- 字体颜色：蓝色 (#2F5597)
- 背景颜色：浅蓝色 (#F0F8FF)
- 边框样式：无
- 行高：自动

**时间轴表头年区域**
- 字体：Calibri
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深蓝色 (#1F4E79)
- 边框样式：全部
- 行高：自动

**时间轴表头月区域**
- 字体：Calibri
- 字号：10
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：蓝色 (#2F5597)
- 边框样式：全部
- 行高：自动

**时间轴表头周区域**
- 字体：Calibri
- 字号：9
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：浅蓝色 (#5B9BD5)
- 边框样式：全部
- 行高：自动

**任务类区域**
- 字体：Calibri
- 字号：11
- 粗体：是
- 字体颜色：深蓝色 (#1F4E79)
- 背景颜色：浅蓝色 (#DEEBF7)
- 边框样式：轮廓
- 列宽：30
- 文本对齐：居中

### 主题4：绿色主题

**全局设置**
- 背景颜色：浅绿色 (#F0FFF0)
- 交替行颜色：更浅的绿色 (#E6FFE6)
- 网格线颜色：淡绿色 (#B0DEB0)
- 网格线区域：仅甘特图区域
- 网格线类型：仅水平
- 字体：Segoe UI
- 字号：9.5
- A列宽度：5

**项目名称区域**
- 字体：Segoe UI
- 字号：14
- 粗体：是
- 字体颜色：深绿色 (#375623)
- 背景颜色：浅绿色 (#F0FFF0)
- 边框样式：底部
- 行高：自动

**项目经理名称区域**
- 字体：Segoe UI
- 字号：11
- 粗体：否
- 字体颜色：绿色 (#548235)
- 背景颜色：浅绿色 (#F0FFF0)
- 边框样式：无
- 行高：自动

**补充信息区域**
- 字体：Segoe UI
- 字号：9.5
- 粗体：否
- 字体颜色：绿色 (#548235)
- 背景颜色：浅绿色 (#F0FFF0)
- 边框样式：无
- 行高：自动

**时间轴表头年区域**
- 字体：Segoe UI
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深绿色 (#375623)
- 边框样式：全部
- 行高：自动

**时间轴表头月区域**
- 字体：Segoe UI
- 字号：10
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：绿色 (#548235)
- 边框样式：全部
- 行高：自动

**时间轴表头周区域**
- 字体：Segoe UI
- 字号：9
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：浅绿色 (#70AD47)
- 边框样式：全部
- 行高：自动

**任务类区域**
- 字体：Segoe UI
- 字号：10
- 粗体：是
- 字体颜色：深绿色 (#375623)
- 背景颜色：浅绿色 (#E2EFDA)
- 边框样式：轮廓
- 列宽：30
- 文本对齐：居中

### 主题5：高对比度主题

**全局设置**
- 背景颜色：白色 (#FFFFFF)
- 交替行颜色：中灰色 (#E0E0E0)
- 网格线颜色：黑色 (#000000)
- 网格线区域：所有区域
- 网格线类型：水平和垂直
- 字体：Arial
- 字号：10
- A列宽度：5

**项目名称区域**
- 字体：Arial
- 字号：14
- 粗体：是
- 字体颜色：黑色 (#000000)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：全部
- 行高：自动

**项目经理名称区域**
- 字体：Arial
- 字号：11
- 粗体：否
- 字体颜色：黑色 (#000000)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：全部
- 行高：自动

**补充信息区域**
- 字体：Arial
- 字号：10
- 粗体：否
- 字体颜色：黑色 (#000000)
- 背景颜色：白色 (#FFFFFF)
- 边框样式：全部
- 行高：自动

**时间轴表头年区域**
- 字体：Arial
- 字号：11
- 粗体：是
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：黑色 (#000000)
- 边框样式：全部
- 行高：自动

**时间轴表头月区域**
- 字体：Arial
- 字号：10
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：深灰色 (#404040)
- 边框样式：全部
- 行高：自动

**时间轴表头周区域**
- 字体：Arial
- 字号：9
- 粗体：否
- 字体颜色：白色 (#FFFFFF)
- 背景颜色：灰色 (#808080)
- 边框样式：全部
- 行高：自动

**任务类区域**
- 字体：Arial
- 字号：11
- 粗体：是
- 字体颜色：黑色 (#000000)
- 背景颜色：浅灰色 (#D9D9D9)
- 边框样式：全部
- 列宽：30
- 自动换行：是
- 文本对齐：居中

### 自定义主题

当 ChartTheme 设置为 "custom" 时，将使用配置表中的自定义配置项。以下是各配置项及其默认值：

**全局设置**
- 背景颜色：从 ChartBackgroundColor 获取（默认：#FFFFFF）
- 交替行颜色：从 ChartAlternateRowColor 获取（默认：#F5F5F5）
- 网格线颜色：从 ChartGridlineColor 获取（默认：#DDDDDD）
- 网格线区域：从 ChartGridlinesArea 获取（默认：all）
- 网格线类型：从 ChartGridlinesType 获取（默认：all）
- A列宽度：从 ColumnAWidth 获取（默认：5）

**项目名称区域**
- 字体：从 ProjectNameFont 获取（默认：Barlow）
- 字号：从 ProjectNameFontSize 获取（默认：14）
- 粗体：从 ProjectNameFontBold 获取（默认：True）
- 字体颜色：从 ProjectNameFontColor 获取（默认：#000000）
- 背景颜色：从 ProjectNameBackColor 获取（默认：#FFFFFF）
- 边框样式：从 ProjectNameBorderStyle 获取（默认：none）
- 行高：从 ProjectNameRowHeight 获取（默认：auto）

**项目经理名称区域**
- 字体：从 ProjectManagerFont 获取（默认：Barlow）
- 字号：从 ProjectManagerFontSize 获取（默认：11）
- 粗体：从 ProjectManagerFontBold 获取（默认：False）
- 字体颜色：从 ProjectManagerFontColor 获取（默认：#333333）
- 背景颜色：从 ProjectManagerBackColor 获取（默认：#FFFFFF）
- 边框样式：从 ProjectManagerBorderStyle 获取（默认：none）
- 行高：从 ProjectManagerRowHeight 获取（默认：auto）

**补充信息区域**
- 字体：从 SupplementInfoFont 获取（默认：Barlow）
- 字号：从 SupplementInfoFontSize 获取（默认：10）
- 粗体：从 SupplementInfoFontBold 获取（默认：False）
- 字体颜色：从 SupplementInfoFontColor 获取（默认：#333333）
- 背景颜色：从 SupplementInfoBackColor 获取（默认：#FFFFFF）
- 边框样式：从 SupplementInfoBorderStyle 获取（默认：none）
- 行高：从 SupplementInfoRowHeight 获取（默认：auto）

**时间轴表头年区域**
- 字体：从 TimelineYearFont 获取（默认：Barlow）
- 字号：从 TimelineYearFontSize 获取（默认：11）
- 粗体：从 TimelineYearFontBold 获取（默认：True）
- 字体颜色：从 TimelineYearFontColor 获取（默认：#FFFFFF）
- 背景颜色：从 TimelineYearBackColor 获取（默认：#4472C4）
- 边框样式：从 TimelineYearBorderStyle 获取（默认：all）
- 行高：从 TimelineYearRowHeight 获取（默认：auto）

**时间轴表头月区域**
- 字体：从 TimelineMonthFont 获取（默认：Barlow）
- 字号：从 TimelineMonthFontSize 获取（默认：10）
- 粗体：从 TimelineMonthFontBold 获取（默认：False）
- 字体颜色：从 TimelineMonthFontColor 获取（默认：#FFFFFF）
- 背景颜色：从 TimelineMonthBackColor 获取（默认：#5B9BD5）
- 边框样式：从 TimelineMonthBorderStyle 获取（默认：all）
- 行高：从 TimelineMonthRowHeight 获取（默认：auto）

**时间轴表头周区域**
- 字体：从 TimelineWeekFont 获取（默认：Barlow）
- 字号：从 TimelineWeekFontSize 获取（默认：9）
- 粗体：从 TimelineWeekFontBold 获取（默认：False）
- 字体颜色：从 TimelineWeekFontColor 获取（默认：#FFFFFF）
- 背景颜色：从 TimelineWeekBackColor 获取（默认：#70AD47）
- 边框样式：从 TimelineWeekBorderStyle 获取（默认：all）
- 行高：从 TimelineWeekRowHeight 获取（默认：auto）

**任务类区域**
- 字体：从 CategoryFont 获取（默认：Barlow）
- 字号：从 CategoryFontSize 获取（默认：11）
- 粗体：从 CategoryFontBold 获取（默认：True）
- 字体颜色：从 CategoryFontColor 获取（默认：#000000）
- 背景颜色：从 CategoryBackColor 获取（默认：#F2F2F2）
- 边框样式：从 CategoryBorderStyle 获取（默认：outline）
- 列宽：从 CategoryColumnWidth 获取（默认：30）
- 自动换行：从 CategoryWrapText 获取（默认：True）

## 工作区域划分

函数将甘特图工作表划分为以下区域，以便应用不同的样式设置：

### 表头区域（第1-5行，从B列开始，A列保持默认）

1. **项目名称区域**：位置 Range(Cells(1, 2), Cells(1, endCol))
2. **项目经理名称区域**：位置 Range(Cells(2, 2), Cells(2, endCol))
3. **补充信息名称区域**：位置 Range(Cells(3, 2), Cells(5, 2))
4. **时间轴表头年区域**：位置 Range(Cells(3, 3), Cells(3, endCol))
5. **时间轴表头月区域**：位置 Range(Cells(4, 3), Cells(4, endCol))
6. **时间轴表头周区域**：位置 Range(Cells(5, 3), Cells(5, endCol))

### 甘特图主体区域

1. **甘特图任务及里程碑绘图区域**：位置 Range("C6") 到甘特图右下角之间的范围，包含任务条和里程碑及对应的标签文本
2. **任务类区域**：位置 Range("B6") 到 B 列甘特图底部之间的范围

### 甘特图边界确定

甘特图的边界区域通过使用 UsedRange 属性确定：

```vba
' 刷新使用范围
Application.CalculateFull
ws.UsedRange

' 获取工作表使用范围
Dim usedRng As Range
Set usedRng = ws.UsedRange

' 确定最后一行和最后一列
Dim lastRow As Long, endCol As Long
lastRow = usedRng.Row + usedRng.Rows.Count - 1
endCol = usedRng.Column + usedRng.Columns.Count - 1

' 确保至少包含基本甘特图区域
If lastRow < 6 Then lastRow = 6
If endCol < 3 Then endCol = 3

' 定义甘特图任务及里程碑绘图区域
Set ganttDrawingRange = ws.Range(ws.Cells(6, 3), ws.Cells(lastRow, endCol))

' 定义任务类区域
Set taskCategoryRange = ws.Range(ws.Cells(6, 2), ws.Cells(lastRow, 2))
```

### 其他区域

1. **A列区域**：甘特图的 A 列，用于设置固定宽度

## 样式配置选项

### 网格线应用区域选项

通过配置项 `ChartGridlinesArea`，可以控制网格线应用的区域：

| 选项值 | 说明 |
| ------ | ---- |
| all | 所有区域都应用网格线（表头区域(B1:endCol,5)、甘特图任务及里程碑绘图区域和任务类区域） |
| header | 只在表头区域应用网格线（B1:endCol,5） |
| ganttDrawing | 只在甘特图任务及里程碑绘图区域应用网格线 |
| taskCategory | 只在任务类区域应用网格线 |
| header,ganttDrawing | 在表头和甘特图任务及里程碑绘图区域应用网格线 |
| header,taskCategory | 在表头和任务类区域应用网格线 |
| ganttDrawing,taskCategory | 在甘特图任务及里程碑绘图区域和任务类区域应用网格线 |

### 网格线类型选项

通过配置项 `ChartGridlinesType`，可以控制网格线的类型：

| 选项值 | 说明 |
| ------ | ---- |
| all 或 both | 应用水平和垂直网格线 |
| horizontal | 只应用水平网格线 |
| vertical | 只应用垂直网格线 |

### 边框样式选项

通过各区域的 `BorderStyle` 配置项，可以控制边框的样式。边框样式仅控制区域的外边框，内部边框由网格线设置控制：

| 选项值 | 说明 |
| ------ | ---- |
| none | 无边框 |
| all | 外边框（上、下、左、右） |
| outline | 只有外边框（上、下、左、右） |
| bottom | 只有底部边框 |

> 注意：`all` 和 `outline` 的效果相同，都只应用外边框。这是为了避免与网格线设置重叠。

### 行高设置选项

通过各区域的 `RowHeight` 配置项，可以控制行高：

| 选项值 | 说明 |
| ------ | ---- |
| auto | 自动调整行高（使用 Rows.AutoFit） |
| 数值 | 指定固定的行高值（例如：20） |

## 执行流程

1. 检查工作表参数是否有效，如果无效则抛出错误
2. 检查工作表名称是否为"GanttChart"，如果不是则记录警告但继续执行
3. 获取UI模块配置（不使用默认值，以便检查配置项是否存在）
4. 确定主题值：
   - 如果ChartTheme在原始配置中存在（即在配置表中且启用），则获取主题值
   - 如果ChartTheme不存在或被禁用，则使用默认主题1
5. 创建主题配置字典（通过CreateThemeConfig函数）：
   - 如果是预设主题（1-5）：
     - 获取默认配置作为基础
     - 根据主题编号修改特定配置
   - 如果是自定义主题（custom）：
     - 使用配置表中的值创建配置
   - 如果是无效的主题值，使用默认主题1
6. 应用主题配置（通过ApplyThemeConfig函数）：
   - 刷新使用范围并确定甘特图边界
   - 获取全局配置（网格线区域、类型、颜色等）
   - 定义各个区域（表头、甘特图绘图区域、任务类区域等）
   - 设置A列宽度
   - 关闭Excel默认网格线显示
   - 设置工作表背景颜色（从B列开始，A列保持默认）
   - 设置交替行背景颜色（从第6行开始，每隔一行设置一次，仅在甘特图实际使用范围内）
   - 应用各区域的字体、颜色、对齐方式等样式（不包括边框样式）
   - 清除所有现有边框
   - 根据配置的网格线区域和类型，应用自定义网格线
   - 应用各区域的边框样式

### 流程图

```mermaid
flowchart TD
    A[开始] --> B{检查工作表参数是否有效}
    B -->|无效| C[抛出错误]
    B -->|有效| D{检查工作表名称是否为GanttChart}
    D -->|否| E[记录警告但继续执行]
    D -->|是| F[获取UI模块配置\n不使用默认值]
    E --> F
    F --> G{检查ChartTheme是否存在且启用}
    G -->|是| H[获取主题值]
    G -->|否| I[使用默认主题1]
    H --> J[创建主题配置字典]
    I --> J
    J --> K{主题值类型?}
    K -->|数字1-5| L[获取默认配置作为基础]
    K -->|custom| M[使用配置表中的值]
    K -->|无效| N[使用默认主题1]
    L --> O[根据主题编号修改特定配置]
    O --> P[应用主题配置]
    M --> P
    N --> P
    P --> Q[刷新使用范围并确定甘特图边界]
    Q --> R[获取全局配置]
    R --> S[定义各个区域]
    S --> T[设置A列宽度]
    T --> U[关闭Excel默认网格线]
    U --> V[设置工作表背景颜色]
    V --> W[设置交替行颜色]
    W --> X[应用各区域的字体、颜色等样式]
    X --> Y[清除所有现有边框]
    Y --> Z[应用自定义网格线]
    Z --> AA[应用各区域的边框样式]
    AA --> AB[结束]
```

### 详细调用接口

函数使用以下主要接口和方法：

1. **参数检查**
   ```vba
   ' 检查工作表参数是否有效
   If ws Is Nothing Then
       modDebug.LogError 91, "工作表参数无效", "modUI.ApplyGanttTheme"
       Err.Raise 91, "modUI.ApplyGanttTheme", "工作表参数无效"
       Exit Sub
   End If

   ' 检查工作表名称是否为"GanttChart"
   If ws.Name <> "GanttChart" Then
       modDebug.LogWarning "工作表名称不是'GanttChart'，而是'" & ws.Name & "'", "modUI.ApplyGanttTheme"
   End If
   ```

2. **配置获取**
   ```vba
   ' 获取UI模块配置（不使用默认值，以便检查配置项是否存在）
   Dim uiConfigRaw As Dictionary
   Set uiConfigRaw = GetModuleConfig("UI")

   ' 确定主题值
   Dim themeValue As String
   If uiConfigRaw.Exists("ChartTheme") Then
       themeValue = CStr(uiConfigRaw("ChartTheme"))
       modDebug.LogInfo "使用配置表中的ChartTheme值: " & themeValue, "modUI.ApplyGanttTheme"
   Else
       ' 如果ChartTheme不存在或被禁用，则使用默认主题1
       themeValue = "1"
       modDebug.LogInfo "ChartTheme配置项不存在或被禁用，使用默认主题1", "modUI.ApplyGanttTheme"
   End If
   ```

3. **主题配置创建**
   ```vba
   ' 创建主题配置字典
   Dim themeConfig As Dictionary
   Set themeConfig = CreateThemeConfig(themeValue)
   ```

   **CreateThemeConfig函数**
   ```vba
   Private Function CreateThemeConfig(themeValue As String) As Dictionary
       ' 如果是预设主题
       If IsNumeric(themeValue) Then
           Dim themeNumber As Integer
           themeNumber = CInt(themeValue)

           ' 检查是否是预设主题（1-5）
           If themeNumber >= 1 And themeNumber <= 5 Then
               ' 获取默认配置作为基础
               Dim defaultConfig As Dictionary
               Set defaultConfig = modConfigDefaults.GetUIDefaults()

               ' 复制所有默认配置
               Dim key As Variant
               For Each key In defaultConfig.Keys
                   themeConfig.Add key, defaultConfig(key)
               Next key

               ' 根据主题编号修改特定配置
               Select Case themeNumber
                   Case 1
                       ' 主题1: 经典浅色主题 - 使用默认值，无需修改
                   Case 2
                       ' 主题2: 深色主题
                       themeConfig("ChartBackgroundColor") = "#333333"
                       ' ... 其他配置修改
               End Select
           Else
               ' 无效的主题编号，使用默认主题1
               Set themeConfig = modConfigDefaults.GetUIDefaults()
           End If
       ElseIf LCase(themeValue) = "custom" Then
           ' 自定义主题 - 使用配置表中的值
           Dim uiConfig As Dictionary
           Set uiConfig = GetModuleConfig("UI", modConfigDefaults.GetUIDefaults())

           ' 复制所有配置
           Dim customKey As Variant
           For Each customKey In uiConfig.Keys
               themeConfig.Add customKey, uiConfig(customKey)
           Next customKey
       Else
           ' 无效的主题值，使用默认主题1
           Set themeConfig = modConfigDefaults.GetUIDefaults()
       End If
   End Function
   ```

4. **主题配置应用**
   ```vba
   ' 应用主题配置
   ApplyThemeConfig ws, themeConfig
   ```

   **ApplyThemeConfig函数**
   ```vba
   Private Sub ApplyThemeConfig(ws As Worksheet, themeConfig As Dictionary)
       ' 刷新使用范围并确定甘特图边界
       Application.CalculateFull
       Dim temp As Range
       Set temp = ws.UsedRange

       ' 获取工作表使用范围
       Dim usedRng As Range
       Set usedRng = ws.UsedRange

       ' 确定最后一行和最后一列
       Dim lastRow As Long, endCol As Long
       lastRow = usedRng.Row + usedRng.Rows.Count - 1
       endCol = usedRng.Column + usedRng.Columns.Count - 1

       ' 获取全局配置
       Dim gridlinesArea As String
       gridlinesArea = CStr(themeConfig("ChartGridlinesArea"))

       Dim gridlinesType As String
       gridlinesType = CStr(themeConfig("ChartGridlinesType"))

       Dim gridlineColor As String
       gridlineColor = CStr(themeConfig("ChartGridlineColor"))

       Dim backgroundColor As String
       backgroundColor = CStr(themeConfig("ChartBackgroundColor"))

       Dim alternateRowColor As String
       alternateRowColor = CStr(themeConfig("ChartAlternateRowColor"))

       ' 设置A列宽度
       Dim columnAWidth As Double
       columnAWidth = CDbl(themeConfig("ColumnAWidth"))
       ws.Columns("A").ColumnWidth = columnAWidth

       ' 应用各区域样式
       ' ... 项目名称区域、项目经理名称区域等样式设置

       ' 设置背景颜色（从B列开始，A列保持默认）
       ws.Range(ws.Cells(1, 2), ws.Cells(lastRow, endCol)).Interior.Color = modUtilities.GetRGBColor(backgroundColor)

       ' 设置交替行颜色
       Dim i As Long
       Dim lastCol As String
       lastCol = Split(ws.Cells(1, endCol).Address, "$")(1)

       For i = 6 To lastRow Step 2
           ws.Range("C" & i & ":" & lastCol & i).Interior.Color = modUtilities.GetRGBColor(alternateRowColor)
       Next i

       ' 关闭Excel默认网格线显示
       ActiveWindow.DisplayGridlines = False

       ' 清除所有现有的边框（除了已经应用的边框样式）
       headerRange.Borders(xlInsideHorizontal).LineStyle = xlNone
       headerRange.Borders(xlInsideVertical).LineStyle = xlNone
       ganttDrawingRange.Borders.LineStyle = xlNone

       ' 根据区域设置应用网格线
       Select Case LCase(gridlinesArea)
           Case "all"
               ' 所有区域都应用网格线
               ApplyGridlines headerRange, gridlinesType, gridlineColor
               ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor
               ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor
           ' ... 其他区域组合
       End Select
   End Sub
   ```

5. **网格线应用**
   ```vba
   Private Sub ApplyGridlines(targetRange As Range, gridlinesType As String, gridlineColor As String)
       ' 根据网格线类型设置
       Select Case LCase(gridlinesType)
           Case "horizontal"
               ' 只应用水平网格线
               With targetRange.Borders(xlInsideHorizontal)
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(gridlineColor)
                   .Weight = xlThin
               End With

           Case "vertical"
               ' 只应用垂直网格线
               With targetRange.Borders(xlInsideVertical)
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(gridlineColor)
                   .Weight = xlThin
               End With

           Case "all", "both"
               ' 应用水平和垂直网格线
               With targetRange.Borders(xlInsideHorizontal)
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(gridlineColor)
                   .Weight = xlThin
               End With

               With targetRange.Borders(xlInsideVertical)
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(gridlineColor)
                   .Weight = xlThin
               End With

           Case Else
               ' 默认不应用网格线
       End Select
   End Sub
   ```

6. **边框样式应用**
   ```vba
   Private Sub ApplyBorderStyle(targetRange As Range, borderStyle As String, borderColor As String)
       ' 根据边框样式设置
       Select Case LCase(borderStyle)
           Case "none"
               ' 无边框
               targetRange.Borders.LineStyle = xlNone

           Case "all"
               ' 所有边框
               With targetRange.Borders
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(borderColor)
                   .Weight = xlThin
               End With

           Case "outline"
               ' 只有外边框
               With targetRange.BorderAround
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(borderColor)
                   .Weight = xlThin
               End With

           Case "bottom"
               ' 只有底部边框
               With targetRange.Borders(xlEdgeBottom)
                   .LineStyle = xlContinuous
                   .Color = modUtilities.GetRGBColor(borderColor)
                   .Weight = xlThin
               End With

           Case Else
               ' 默认不应用边框
       End Select
   End Sub
   ```

7. **颜色转换**
   ```vba
   ' 获取RGB颜色值
   Public Function GetRGBColor(colorHex As String) As Long
       On Error GoTo ErrorHandler

       Dim r As Long, g As Long, b As Long

       ' 移除可能的#前缀
       If Left(colorHex, 1) = "#" Then
           colorHex = Mid(colorHex, 2)
       End If

       ' 解析十六进制颜色值
       r = CLng("&H" & Mid(colorHex, 1, 2))
       g = CLng("&H" & Mid(colorHex, 3, 2))
       b = CLng("&H" & Mid(colorHex, 5, 2))

       ' 返回RGB值
       GetRGBColor = RGB(r, g, b)
       Exit Function

   ErrorHandler:
       modDebug.LogError Err.Number, Err.Description, "modUtilities.GetRGBColor"
       GetRGBColor = RGB(0, 0, 0) ' 返回黑色作为默认值
   End Function
   ```

## 辅助函数

`ApplyGanttTheme` 函数使用以下三个主要辅助函数：

### 1. CreateThemeConfig 函数

```vba
Private Function CreateThemeConfig(themeValue As String) As Dictionary
```

该辅助函数根据指定的主题值创建主题配置字典。它支持预设主题（1-5）和自定义主题（custom）。

#### CreateThemeConfig流程图

```mermaid
flowchart TD
    A[开始] --> B{检查themeValue类型}
    B -->|数字| C{是否是1-5之间}
    B -->|"custom"| D[获取配置表中的自定义配置]
    B -->|其他| E[使用默认主题1]
    C -->|是| F[获取默认配置作为基础]
    C -->|否| E
    F --> G{根据主题编号}
    G -->|1| H[使用默认值\n无需修改]
    G -->|2| I[设置深色主题配置]
    G -->|3| J[设置蓝色主题配置]
    G -->|4| K[设置绿色主题配置]
    G -->|5| L[设置高对比度主题配置]
    H --> M[返回主题配置]
    I --> M
    J --> M
    K --> M
    L --> M
    D --> M
    E --> M
    M --> N[结束]
```

#### CreateThemeConfig调用接口

函数使用以下主要接口和方法：

1. **获取默认配置**
   ```vba
   Dim defaultConfig As Dictionary
   Set defaultConfig = modConfigDefaults.GetUIDefaults()
   ```

2. **复制配置项**
   ```vba
   ' 复制所有默认配置
   Dim key As Variant
   For Each key In defaultConfig.Keys
       themeConfig.Add key, defaultConfig(key)
   Next key
   ```

3. **修改特定配置**
   ```vba
   ' 根据主题编号修改特定配置
   Select Case themeNumber
       Case 1
           ' 主题1: 经典浅色主题 - 使用默认值，无需修改
       Case 2
           ' 主题2: 深色主题
           themeConfig("ChartBackgroundColor") = "#333333"
           themeConfig("ChartAlternateRowColor") = "#444444"
           themeConfig("ChartGridlineColor") = "#555555"
           ' ... 其他配置修改
   End Select
   ```

4. **获取自定义配置**
   ```vba
   ' 自定义主题 - 使用配置表中的值
   Dim uiConfig As Dictionary
   Set uiConfig = GetModuleConfig("UI", modConfigDefaults.GetUIDefaults())
   ```

### 2. ApplyThemeConfig 函数

```vba
Private Sub ApplyThemeConfig(ws As Worksheet, themeConfig As Dictionary)
```

该辅助函数将主题配置应用到指定的工作表。它处理甘特图的所有视觉元素，包括背景颜色、交替行颜色、网格线、字体、边框等。

#### ApplyThemeConfig流程图

```mermaid
flowchart TD
    A[开始] --> B[刷新使用范围并确定甘特图边界]
    B --> C[获取全局配置]
    C --> D[定义各个区域]
    D --> E[设置A列宽度]
    E --> F[关闭Excel默认网格线]
    F --> G[设置工作表背景颜色]
    G --> H[设置交替行颜色]
    H --> I[清除现有边框]
    I --> J{根据gridlinesArea选择区域}
    J -->|all| K[应用所有区域网格线]
    J -->|header| L[应用表头区域网格线]
    J -->|ganttDrawing| M[应用甘特图绘图区域网格线]
    J -->|taskCategory| N[应用任务类区域网格线]
    J -->|组合| O[应用组合区域网格线]
    K --> P[应用项目名称区域样式]
    L --> P
    M --> P
    N --> P
    O --> P
    P --> Q[应用项目经理名称区域样式]
    Q --> R[应用补充信息区域样式]
    R --> S[应用时间轴表头年区域样式]
    S --> T[应用时间轴表头月区域样式]
    T --> U[应用时间轴表头周区域样式]
    U --> V[应用任务类区域样式]
    V --> W[结束]
```

#### ApplyThemeConfig调用接口

函数使用以下主要接口和方法，按执行顺序排列：

1. **刷新使用范围**
   ```vba
   Application.CalculateFull
   Dim temp As Range
   Set temp = ws.UsedRange
   ```

2. **获取工作表使用范围**
   ```vba
   Dim usedRng As Range
   Set usedRng = ws.UsedRange

   ' 确定最后一行和最后一列
   Dim lastRow As Long, endCol As Long
   lastRow = usedRng.Row + usedRng.Rows.Count - 1
   endCol = usedRng.Column + usedRng.Columns.Count - 1
   ```

3. **设置A列宽度**
   ```vba
   ' 设置A列宽度
   Dim columnAWidth As Double
   columnAWidth = CDbl(themeConfig("ColumnAWidth"))
   ws.Columns("A").ColumnWidth = columnAWidth
   ```

4. **关闭Excel默认网格线**
   ```vba
   ActiveWindow.DisplayGridlines = False
   ```

5. **设置背景颜色**
   ```vba
   ' 设置背景颜色（从B列开始，A列保持默认）
   ws.Range(ws.Cells(1, 2), ws.Cells(lastRow, endCol)).Interior.Color = modUtilities.GetRGBColor(backgroundColor)
   ```

6. **设置交替行颜色**
   ```vba
   ' 设置交替行颜色
   Dim i As Long
   Dim lastCol As String
   lastCol = Split(ws.Cells(1, endCol).Address, "$")(1)

   For i = 6 To lastRow Step 2
       ws.Range("C" & i & ":" & lastCol & i).Interior.Color = modUtilities.GetRGBColor(alternateRowColor)
   Next i
   ```

7. **清除现有边框**
   ```vba
   headerRange.Borders(xlInsideHorizontal).LineStyle = xlNone
   headerRange.Borders(xlInsideVertical).LineStyle = xlNone
   ganttDrawingRange.Borders.LineStyle = xlNone
   ```

8. **应用网格线**
   ```vba
   Select Case LCase(gridlinesArea)
       Case "all"
           ' 所有区域都应用网格线
           ApplyGridlines headerRange, gridlinesType, gridlineColor
           ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor
           ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor
       ' ... 其他区域组合
   End Select
   ```

9. **设置区域样式**
   ```vba
   ' 应用项目名称区域样式
   With ws.Cells(1, 2)
       .Font.Name = CStr(themeConfig("ProjectNameFont"))
       .Font.Size = CDbl(themeConfig("ProjectNameFontSize"))
       .Font.Bold = CBool(themeConfig("ProjectNameFontBold"))
       .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectNameFontColor")))
       .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectNameBackColor")))
       ApplyBorderStyle .Cells, CStr(themeConfig("ProjectNameBorderStyle")), gridlineColor
       ' ... 设置行高
   End With
   ```

10. **应用任务类区域样式**
    ```vba
    ' 应用任务类区域样式
    With taskCategoryRange
        .Font.Name = CStr(themeConfig("CategoryFont"))
        .Font.Size = CDbl(themeConfig("CategoryFontSize"))
        .Font.Bold = CBool(themeConfig("CategoryFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("CategoryFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("CategoryBackColor")))
        ApplyBorderStyle .Cells, CStr(themeConfig("CategoryBorderStyle")), gridlineColor
        .WrapText = CBool(themeConfig("CategoryWrapText"))
        .ColumnWidth = CDbl(themeConfig("CategoryColumnWidth"))
    End With
    ```

### 3. ApplyGridlines 函数

```vba
Private Sub ApplyGridlines(targetRange As Range, gridlinesType As String, gridlineColor As String)
```

该辅助函数根据指定的网格线类型（水平、垂直或两者）和颜色，为目标区域应用网格线。

#### ApplyGridlines流程图

```mermaid
flowchart TD
    A[开始] --> B{检查gridlinesType}
    B -->|horizontal| C[只应用水平网格线]
    B -->|vertical| D[只应用垂直网格线]
    B -->|all/both| E[应用水平和垂直网格线]
    B -->|其他| F[不应用网格线]
    C --> G[设置水平网格线颜色和样式]
    D --> H[设置垂直网格线颜色和样式]
    E --> I[设置水平和垂直网格线颜色和样式]
    G --> J[结束]
    H --> J
    I --> J
    F --> J
```

#### ApplyGridlines调用接口

函数使用以下主要接口和方法：

1. **水平网格线设置**
   ```vba
   With targetRange.Borders(xlInsideHorizontal)
       .LineStyle = xlContinuous
       .Color = modUtilities.GetRGBColor(gridlineColor)
       .Weight = xlThin
   End With
   ```

2. **垂直网格线设置**
   ```vba
   With targetRange.Borders(xlInsideVertical)
       .LineStyle = xlContinuous
       .Color = modUtilities.GetRGBColor(gridlineColor)
       .Weight = xlThin
   End With
   ```

### 4. ApplyBorderStyle 函数

```vba
Private Sub ApplyBorderStyle(targetRange As Range, borderStyle As String, borderColor As String)
```

该辅助函数根据指定的边框样式（无、全部、轮廓或底部）和颜色，为目标区域应用边框。

#### ApplyBorderStyle流程图

```mermaid
flowchart TD
    A[开始] --> B{检查borderStyle}
    B -->|none| C[清除所有边框]
    B -->|all| D[应用所有边框]
    B -->|outline| E[只应用外边框]
    B -->|bottom| F[只应用底部边框]
    B -->|其他| G[不应用边框]
    C --> H[结束]
    D --> H
    E --> H
    F --> H
    G --> H
```

#### ApplyBorderStyle调用接口

函数使用以下主要接口和方法：

1. **清除边框**
   ```vba
   targetRange.Borders.LineStyle = xlNone
   ```

2. **应用所有边框**
   ```vba
   With targetRange.Borders
       .LineStyle = xlContinuous
       .Color = modUtilities.GetRGBColor(borderColor)
       .Weight = xlThin
   End With
   ```

3. **应用外边框**
   ```vba
   With targetRange.BorderAround
       .LineStyle = xlContinuous
       .Color = modUtilities.GetRGBColor(borderColor)
       .Weight = xlThin
   End With
   ```

4. **应用底部边框**
   ```vba
   With targetRange.Borders(xlEdgeBottom)
       .LineStyle = xlContinuous
       .Color = modUtilities.GetRGBColor(borderColor)
       .Weight = xlThin
   End With
   ```

## 使用示例

在 `modMain.bas` 中的 `GenerateGanttChart` 函数中，在甘特图创建完成后调用 `ApplyGanttTheme` 函数：

```vba
' 4. 应用甘特图主题
modDebug.LogInfo "步骤4: 开始应用甘特图主题", "modMain.GenerateGanttChart"
ApplyGanttTheme
modDebug.LogInfo "甘特图主题应用完成", "modMain.GenerateGanttChart"
```

其中，`ApplyGanttTheme` 是 `modMain.bas` 中的一个私有函数，它调用 `modUI.ApplyGanttTheme` 函数：

```vba
' 应用甘特图主题
Private Sub ApplyGanttTheme()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.ApplyGanttTheme"

    ' 获取甘特图工作表
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 调用UI模块的主题应用函数
    modUI.ApplyGanttTheme ws

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.ApplyGanttTheme"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.ApplyGanttTheme"
    Err.Raise Err.Number, "modMain.ApplyGanttTheme", Err.Description
End Sub
```

## 主题配置系统

### 预设主题

函数提供了5种预设主题，每种主题都有不同的视觉风格：

1. **主题1：经典浅色主题** - 使用默认配置，适合大多数场景
2. **主题2：深色主题** - 使用深色背景和对比色，适合在暗光环境下查看
3. **主题3：蓝色主题** - 使用蓝色调配色方案，给人专业、冷静的感觉
4. **主题4：绿色主题** - 使用绿色调配色方案，给人自然、和谐的感觉
5. **主题5：高对比度主题** - 使用高对比度配色，适合需要清晰辨识的场景

### 自定义主题

如果预设主题不满足需求，可以通过以下方式使用自定义主题：

1. 将 `ChartTheme` 设置为 `custom`，这样函数将使用配置表中的自定义配置项
2. 在配置表中设置各个区域的配置项来自定义样式，所有配置项都有默认值
3. 如果某个配置项未在配置表中设置或被禁用，将使用默认值
4. 自定义主题允许您灵活地调整甘特图的每个区域的样式，而不受预设主题的限制

### 配置项获取流程

1. 首先检查配置项是否在配置表中存在且启用
2. 如果存在且启用，使用配置表中的值
3. 如果不存在或被禁用，使用默认值
4. 默认值由 `modConfigDefaults.GetUIDefaults()` 函数提供

```vba
' 获取UI模块配置（不使用默认值，以便检查配置项是否存在）
Dim uiConfigRaw As Dictionary
Set uiConfigRaw = GetModuleConfig("UI")

' 如果ChartTheme在原始配置中存在（即在配置表中且启用），则获取主题值
If uiConfigRaw.Exists("ChartTheme") Then
    themeValue = CStr(uiConfigRaw("ChartTheme"))
Else
    ' 如果ChartTheme不存在或被禁用，则使用默认主题1
    themeValue = "1"
End If
```

## 注意事项

1. **调用时机**：该函数应在甘特图的所有元素（任务条、里程碑等）绘制完成后调用，以确保主题设置不会被后续操作覆盖

2. **主题选择**：
   - ChartTheme配置项如果在配置表中不存在或被禁用，将使用默认主题1
   - 如果使用预设主题（ChartTheme 值为1-5），则会使用预设主题的配置
   - 如果使用自定义主题（ChartTheme 值为custom），则会使用配置表中的自定义配置项
   - 如果主题值无效（非1-5的数字且不是custom），将使用默认主题1

3. **样式应用**：
   - 背景颜色从B列开始应用，A列保持默认样式
   - 交替行颜色从第6行开始应用（因为前5行是表头区域），且只在甘特图的实际使用范围内生效
   - 交替行颜色从C列开始应用（因为B列是任务类别列）
   - 函数会关闭Excel默认的网格线显示，并使用自定义的网格线代替
   - 应用主题会清除工作表中所有现有的边框，然后重新应用网格线和边框样式

4. **颜色处理**：
   - 函数使用`modUtilities.GetRGBColor`将十六进制颜色代码转换为RGB值
   - 颜色代码格式为"#RRGGBB"，例如"#FF0000"表示红色
   - 如果颜色代码无效，将使用黑色作为默认值

5. **行高和列宽**：
   - 行高设置为"auto"时会使用Excel的AutoFit功能，否则会设置为指定的数值
   - A列宽度由ColumnAWidth配置项控制，默认为5
   - B列（任务类别列）宽度由CategoryColumnWidth配置项控制，默认为30

6. **边框样式**：
   - 边框样式可以设置为none（无）、all（全部）、outline（轮廓）或bottom（底部）
   - 网格线类型可以设置为all/both（水平和垂直）、horizontal（仅水平）或vertical（仅垂直）

7. **区域确定**：
   - 甘特图边界的确定通过 UsedRange 属性实现
   - 在确定边界前使用 Application.CalculateFull 和访问 UsedRange 来刷新使用范围
   - 如果最后一行小于6或最后一列小于3，将使用最小值确保至少包含基本甘特图区域

8. **错误处理**：
   - 函数包含完整的错误处理，记录错误并向上传递
   - 如果工作表参数无效，函数将抛出错误并退出
   - 如果工作表名称不是"GanttChart"，函数将记录警告但继续执行
