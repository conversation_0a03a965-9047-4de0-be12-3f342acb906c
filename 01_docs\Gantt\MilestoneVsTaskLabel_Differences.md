# 里程碑与任务条标签位置计算逻辑差异说明

本文档详细说明里程碑（菱形）和任务条（矩形）在标签位置计算逻辑上的差异。

## 目录

1. [概述](#概述)
2. [水平位置计算差异](#水平位置计算差异)
3. [垂直位置计算差异](#垂直位置计算差异)
4. [内部位置支持差异](#内部位置支持差异)
5. [代码实现](#代码实现)
6. [视觉效果差异](#视觉效果差异)

## 概述

在甘特图系统中，里程碑（菱形）和任务条（矩形）的标签位置计算逻辑存在明显差异。这些差异主要体现在标签的水平位置、垂直位置以及内部位置支持等方面。这些差异的设计目的是为了使标签在视觉上与各自的形状更加协调，提高甘特图的可读性和美观性。

## 水平位置计算差异

### 右侧位置（"right"）

**里程碑（菱形）：**
```vba
' 右侧位置
If isMilestone Then
    ' 里程碑是菱形，需要调整水平距离
    labelX = centerX + taskWidth / 2 * 0.7 + labelDistance
End If
```

**任务条（矩形）：**
```vba
' 右侧位置
labelX = centerX + taskWidth / 2 + labelDistance
```

**关键差异：**
- 里程碑使用`taskWidth * 0.7`计算水平距离，考虑菱形的几何特性
- 任务条直接使用`taskWidth`计算水平距离
- 里程碑的标签距离比任务条的标签距离更短，使标签更靠近菱形

### 左侧位置（"left"）

**里程碑（菱形）：**
```vba
' 左侧位置
If isMilestone Then
    ' 里程碑是菱形，需要调整水平距离
    labelX = centerX - taskWidth / 2 * 0.7 - labelDistance - textWidth
End If
```

**任务条（矩形）：**
```vba
' 左侧位置
labelX = centerX - taskWidth / 2 - labelDistance - textWidth
```

**关键差异：**
- 里程碑使用`taskWidth * 0.7`计算水平距离，考虑菱形的几何特性
- 任务条直接使用`taskWidth`计算水平距离
- 里程碑的标签距离比任务条的标签距离更短，使标签更靠近菱形

## 垂直位置计算差异

### 上方位置（"top"）

**里程碑（菱形）：**
```vba
' 上方位置
labelX = centerX - textWidth / 2  ' 水平居中
labelY = centerY - shape.Height / 2 - labelDistance - textHeight
```

**任务条（矩形）：**
```vba
' 上方位置
labelX = taskLeft  ' 左对齐于任务条左边缘
labelY = centerY - taskHeight / 2 - labelDistance - textHeight
```

**关键差异：**
- 里程碑标签水平居中于菱形（相对于菱形中心点）
- 任务条标签左对齐于任务条左边缘
- 里程碑使用`shape.Height`属性获取菱形的实际高度
- 任务条使用局部变量`taskHeight`（通常等于`shape.Height`）

### 下方位置（"bottom"）

**里程碑（菱形）：**
```vba
' 下方位置
labelX = centerX - textWidth / 2  ' 水平居中
labelY = centerY + shape.Height / 2 + labelDistance
```

**任务条（矩形）：**
```vba
' 下方位置
labelX = taskLeft  ' 左对齐于任务条左边缘
labelY = centerY + taskHeight / 2 + labelDistance
```

**关键差异：**
- 里程碑标签水平居中于菱形（相对于菱形中心点）
- 任务条标签左对齐于任务条左边缘
- 里程碑使用`shape.Height`属性获取菱形的实际高度
- 任务条使用局部变量`taskHeight`（通常等于`shape.Height`）

## 内部位置支持差异

**里程碑（菱形）：**
- 不支持内部位置（"inside"）
- 菱形内部空间有限，不适合放置文本

**任务条（矩形）：**
- 支持内部位置（"inside"）
- 如果标签宽度小于任务条宽度的90%，可以放在内部

**代码实现：**
```vba
' 检查标签是否能放在任务条内部
Dim canFitInside As Boolean
canFitInside = (textWidth <= taskWidth * 0.9) And (textPosition = "" Or textPosition = "inside") ' 如果标签宽度小于任务条宽度的90%且未指定位置或指定为inside

If canFitInside Then
    ' 放在任务条内部，居中对齐
    labelX = centerX - textWidth / 2  ' 水平居中对齐
    labelY = centerY - textHeight / 2
    textAlign = xlCenter
End If
```

## 代码实现

在`AddTaskLabelWithCoordinates2`函数中，通过以下代码判断形状类型：

```vba
' 判断是否为里程碑（菱形）
Dim isMilestone As Boolean
isMilestone = (shape.Type = msoShapeDiamond)
```

然后在后续的位置计算中，根据`isMilestone`的值应用不同的计算逻辑：

```vba
If isMilestone Then
    ' 里程碑特有的位置计算逻辑
Else
    ' 任务条特有的位置计算逻辑
End If
```

## 视觉效果差异

这些计算逻辑的差异会导致以下实际效果：

1. **右侧标签：** 里程碑和任务条的标签都位于形状右侧，与形状保持相同的距离（labelDistance）
2. **左侧标签：** 里程碑和任务条的标签都位于形状左侧，与形状保持相同的距离（labelDistance）
3. **上/下标签：**
   - 里程碑的标签水平居中于菱形，视觉上更加平衡
   - 任务条的标签左对齐于任务条左边缘，更符合阅读习惯
4. **内部标签：** 只有任务条支持内部标签，里程碑不支持

这些差异的设计目的是为了使标签在视觉上与各自的形状更加协调，提高甘特图的可读性和美观性。
