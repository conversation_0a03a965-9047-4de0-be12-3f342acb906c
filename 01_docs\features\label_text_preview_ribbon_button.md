# LabelText预览切换Ribbon按钮功能说明

## 概述

新增了一个切换式的Ribbon按钮"切换标签预览"（Toggle Label Preview），允许用户在taskTable中预览和清空LabelText内容，提高表格的可读性。

## 功能特性

### 🔄 切换式操作
- **第一次点击**：生成并显示LabelText预览内容
- **第二次点击**：清空LabelText内容，恢复表格简洁性
- **智能检测**：自动判断当前状态并执行相应操作

### 📍 按钮位置
- **Ribbon选项卡**：甘特图
- **功能组**：标签工具 (Label Tools)
- **按钮名称**：切换标签预览 (Toggle Label Preview)
- **图标**：ShowDetail (显示详细信息图标)

## 实现细节

### 1. Ribbon按钮配置

#### XML配置
```xml
<group id="grpLabelTools" label="Label Tools">
  <button id="btnToggleLabelTextPreview"
          label="Toggle Label Preview"
          imageMso="ShowDetail"
          size="large"
          onAction="modRibbon.Ribbon_ToggleLabelTextPreview"/>
</group>
```

#### VBA回调函数
```vba
Public Sub Ribbon_ToggleLabelTextPreview(control As IRibbonControl)
    ' 自动切换到Milestones&WBS工作表
    ' 调用切换逻辑
    ToggleLabelTextPreview
End Sub
```

### 2. 核心功能函数

#### 2.1 状态检测
```vba
Private Function DetermineLabelTextState(dataRange As Range, labelCol As Long) As String
    ' 检查前5行是否有LabelText内容
    ' 返回 "HAS_CONTENT" 或 "EMPTY"
End Function
```

#### 2.2 内容清空
```vba
Private Sub ClearLabelTextContent(dataRange As Range, labelCol As Long)
    ' 清空所有LabelText单元格内容
    ' 提供详细的日志记录
End Sub
```

#### 2.3 预览生成
```vba
Private Function GenerateLabelTextPreview() As Boolean
    ' 调用modData.GenerateTaskLabels()函数
    ' 生成基于Tag和Flag的LabelText内容
End Function
```

## 使用流程

### 步骤1：准备数据
1. 在taskTable中输入基本任务信息（Description、Type等）
2. 在Tag1-4列中输入标签内容
3. 在Flag1-4列中输入"Y"启用对应标签

### 步骤2：预览标签
1. 点击Ribbon中的"切换标签预览"按钮
2. 系统自动生成LabelText内容
3. 在LabelText列中查看扩展后的标签文本

### 步骤3：清空预览（可选）
1. 再次点击"切换标签预览"按钮
2. 系统清空LabelText列内容
3. 恢复表格的简洁性

## 使用示例

### 输入数据示例
| Description | Tag1 | Flag1 | Tag2 | Flag2 | LabelText |
|-------------|------|-------|------|-------|-----------|
| 开发用户界面 | 进行中 | Y | 高优先级 | Y | (空) |
| 数据库设计 | 已完成 | Y | | | (空) |

### 点击预览后
| Description | Tag1 | Flag1 | Tag2 | Flag2 | LabelText |
|-------------|------|-------|------|-------|-----------|
| 开发用户界面 | 进行中 | Y | 高优先级 | Y | 开发用户界面 进行中 高优先级 |
| 数据库设计 | 已完成 | Y | | | 数据库设计 已完成 |

### 再次点击清空后
| Description | Tag1 | Flag1 | Tag2 | Flag2 | LabelText |
|-------------|------|-------|------|-------|-----------|
| 开发用户界面 | 进行中 | Y | 高优先级 | Y | (空) |
| 数据库设计 | 已完成 | Y | | | (空) |

## 错误处理

### 1. 缺少LabelText列
- **提示信息**："taskTable中未找到LabelText列，无法执行预览操作。"
- **处理方式**：友好提示，不影响其他功能

### 2. 表格无数据
- **提示信息**："taskTable中没有数据。"
- **处理方式**：提示用户先添加数据

### 3. 生成失败
- **提示信息**："生成LabelText预览失败，请检查数据。"
- **处理方式**：记录详细错误日志，提示用户检查数据

## 技术特点

### ✅ 优势
1. **智能切换**：自动检测当前状态，无需用户判断
2. **用户友好**：清晰的提示信息和错误处理
3. **性能优化**：批量操作，避免逐个单元格处理
4. **日志完整**：详细的调试日志，便于问题排查
5. **向后兼容**：不影响现有功能，可选使用

### 🔧 实现亮点
- **状态检测**：检查前5行内容判断当前状态
- **错误容忍**：各种异常情况都有相应处理
- **工作表切换**：自动切换到正确的工作表
- **模块化设计**：功能分离，便于维护

## 与主功能的关系

1. **独立操作**：不依赖数据验证流程，可单独使用
2. **互补功能**：与自动标签生成功能互补
3. **预览用途**：主要用于预览效果，不影响甘特图生成
4. **可选功能**：用户可选择是否使用，不影响核心流程

## 注意事项

1. **预览性质**：此功能主要用于预览，实际甘特图生成时会重新生成LabelText
2. **数据一致性**：修改Tag或Flag后，需要重新预览以查看最新效果
3. **表格可读性**：建议在编辑数据时清空LabelText，完成后再预览
4. **性能考虑**：大量数据时，生成预览可能需要几秒钟时间
