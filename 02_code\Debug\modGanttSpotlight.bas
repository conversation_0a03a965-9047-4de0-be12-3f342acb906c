
'Attribute VB_Name = "modGanttSpotlight"
Option Explicit

' =========================================================
' 模块: modGanttSpotlight
' 描述: 实现甘特图的聚光灯效果
' =========================================================

' 初始化甘特图聚光灯效果
Public Sub InitializeGanttSpotlight(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGanttSpotlight.InitializeGanttSpotlight"

    ' 检查是否启用聚光灯效果
    Dim enableSpotlight As Boolean
    enableSpotlight = CBool(GetConfig("EnableSpotlight", True))

    If Not enableSpotlight Then
        modDebug.LogInfo "聚光灯效果已禁用，跳过初始化", "modGanttSpotlight.InitializeGanttSpotlight"
        modDebug.LogFunctionExit "modGanttSpotlight.InitializeGanttSpotlight", "跳过 - 功能已禁用"
        Exit Sub
    End If

    ' 获取聚光灯模式配置
    Dim spotlightMode As String
    spotlightMode = CStr(GetConfig("SpotlightMode", "all"))

    ' 确保聚光灯模式值有效
    If spotlightMode <> "horizontal" And spotlightMode <> "vertical" And spotlightMode <> "all" Then
        modDebug.LogWarning "无效的聚光灯模式: " & spotlightMode & "，使用默认值 'all'", "modGanttSpotlight.InitializeGanttSpotlight"
        spotlightMode = "all"
    End If

    ' 获取聚光灯颜色配置
    Dim spotlightColor As String
    spotlightColor = CStr(GetConfig("SpotlightColor", "#E6F2FF"))

    ' 添加调试信息
    modDebug.LogInfo "开始应用聚光灯条件格式，工作表: " & ws.Name & ", 模式: " & spotlightMode & ", 颜色: " & spotlightColor, _
        "modGanttSpotlight.InitializeGanttSpotlight"

    ' 添加工作表代码
    AddWorksheetCode ws, spotlightMode, spotlightColor

    ' 添加调试信息
    modDebug.LogInfo "聚光灯条件格式应用完成", "modGanttSpotlight.InitializeGanttSpotlight"

    modDebug.LogInfo "甘特图聚光灯效果已初始化，模式: " & spotlightMode & "，颜色: " & spotlightColor, "modGanttSpotlight.InitializeGanttSpotlight"
    modDebug.LogFunctionExit "modGanttSpotlight.InitializeGanttSpotlight", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGanttSpotlight.InitializeGanttSpotlight"
End Sub

' 添加工作表代码
Private Sub AddWorksheetCode(ws As Worksheet, spotlightMode As String, spotlightColor As String)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGanttSpotlight.AddWorksheetCode"

    ' 不再尝试修改工作表代码模块，而是直接添加条件格式
    ' 应用条件格式
    ApplySpotlightConditionalFormatting ws, spotlightMode, spotlightColor


    modDebug.LogInfo "已添加条件格式，并提示用户添加工作表代码", "modGanttSpotlight.AddWorksheetCode"
    modDebug.LogFunctionExit "modGanttSpotlight.AddWorksheetCode", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGanttSpotlight.AddWorksheetCode"
End Sub

' 应用聚光灯条件格式
Private Sub ApplySpotlightConditionalFormatting(ws As Worksheet, spotlightMode As String, spotlightColor As String)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGanttSpotlight.ApplySpotlightConditionalFormatting"


    ' 获取甘特图区域的边界
    Dim lastRow As Long, lastCol As Long
    lastRow = ws.UsedRange.Rows(ws.UsedRange.Rows.Count).row
    lastCol = ws.UsedRange.Columns(ws.UsedRange.Columns.Count).Column

    ' 获取RGB颜色值
    Dim rgbColor As Long
    rgbColor = modUtilities.GetRGBColor(spotlightColor)

    ' 添加调试信息
    modDebug.LogInfo "聚光灯颜色: " & spotlightColor & ", RGB值=" & rgbColor, _
        "modGanttSpotlight.ApplySpotlightConditionalFormatting"

    ' 根据聚光灯模式应用条件格式
    ' 首先定义甘特图主体区域（从第6行开始，从C列开始）
    Dim ganttRange As range
    Set ganttRange = ws.range(ws.Cells(5, 2), ws.Cells(lastRow, lastCol))

    ' 清除现有条件格式
    ganttRange.FormatConditions.Delete

    ' 根据聚光灯模式应用条件格式
    Select Case LCase(spotlightMode)
        Case "all"
            ' 同时应用水平和垂直聚光灯 - 使用单一条件格式
            ' 修正公式字符串：移除多余的等号，转义双引号
            ganttRange.FormatConditions.Add Type:=xlExpression, _
                Formula1:="=OR(ROW()=CELL(""row""),COLUMN()=CELL(""col""))" ' <--- 修正这里
            With ganttRange.FormatConditions(ganttRange.FormatConditions.Count) ' 使用 Count 获取刚刚添加的条件格式
                .Interior.Color = rgbColor

                ' 根据背景色亮度自动设置文字颜色
                If IsColorDark(rgbColor) Then
                    .Font.Color = GetRandomLightColor() ' 深色背景使用随机浅色文字
                Else
                    .Font.Color = GetRandomDarkColor() ' 浅色背景使用随机深色文字
                End If

                .StopIfTrue = False
            End With

            modDebug.LogInfo "已应用水平和垂直聚光灯条件格式（合并公式）", _
                "modGanttSpotlight.ApplySpotlightConditionalFormatting"

        Case "horizontal"
            ' 只应用水平聚光灯
            ' 公式字符串正确
            ganttRange.FormatConditions.Add Type:=xlExpression, Formula1:="=ROW()=CELL(""row"")"
            With ganttRange.FormatConditions(ganttRange.FormatConditions.Count) ' 使用 Count 获取刚刚添加的条件格式
                .Interior.Color = rgbColor

                ' 根据背景色亮度自动设置文字颜色
                If IsColorDark(rgbColor) Then
                    .Font.Color = GetRandomLightColor() ' 深色背景使用随机浅色文字
                Else
                    .Font.Color = GetRandomDarkColor() ' 浅色背景使用随机深色文字
                End If

                .StopIfTrue = False
            End With

            modDebug.LogInfo "已应用水平聚光灯条件格式", _
                "modGanttSpotlight.ApplySpotlightConditionalFormatting"

        Case "vertical"
            ' 只应用垂直聚光灯
            ' 修正公式字符串：转义双引号
            ganttRange.FormatConditions.Add Type:=xlExpression, _
                Formula1:="=COLUMN()=CELL(""col"")" ' <--- 修正这里
            With ganttRange.FormatConditions(ganttRange.FormatConditions.Count) ' 使用 Count 获取刚刚添加的条件格式
                .Interior.Color = rgbColor

                ' 根据背景色亮度自动设置文字颜色
                If IsColorDark(rgbColor) Then
                    .Font.Color = GetRandomLightColor() ' 深色背景使用随机浅色文字
                Else
                    .Font.Color = GetRandomDarkColor() ' 浅色背景使用随机深色文字
                End If

                .StopIfTrue = False
            End With

            modDebug.LogInfo "已应用垂直聚光灯条件格式", _
                "modGanttSpotlight.ApplySpotlightConditionalFormatting"

        Case Else
            ' 无效的模式，不应用任何条件格式
            modDebug.LogWarning "无效的聚光灯模式: " & spotlightMode & "，不应用任何条件格式", _
                "modGanttSpotlight.ApplySpotlightConditionalFormatting"
    End Select

    modDebug.LogFunctionExit "modGanttSpotlight.ApplySpotlightConditionalFormatting", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGanttSpotlight.ApplySpotlightConditionalFormatting"
End Sub

' 获取配置值
Private Function GetConfig(configName As String, defaultValue As Variant) As Variant
    On Error GoTo ErrorHandler

    ' 调用公共函数获取配置
    GetConfig = modData.GetConfig(configName, defaultValue)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGanttSpotlight.GetConfig"
    GetConfig = defaultValue
End Function

Private Function IsColorDark(rgbColor As Long) As Boolean
    ' 提取RGB分量
    Dim r As Integer, g As Integer, b As Integer
    r = rgbColor Mod 256
    g = (rgbColor \ 256) Mod 256
    b = (rgbColor \ 65536) Mod 256

    ' 计算亮度（使用感知亮度公式）
    ' 亮度 = 0.299*R + 0.587*G + 0.114*B
    Dim brightness As Double
    brightness = (0.299 * r + 0.587 * g + 0.114 * b)

    ' 亮度小于128认为是深色
    Dim isDark As Boolean
    isDark = (brightness < 128)

    ' 添加调试信息
    modDebug.LogVerbose "颜色亮度分析: R=" & r & ", G=" & g & ", B=" & b & _
        ", 亮度=" & brightness & ", 是深色=" & isDark, _
        "modGanttSpotlight.IsColorDark"

    IsColorDark = isDark
End Function

' 生成随机高饱和度浅色
Private Function GetRandomLightColor() As Long
    On Error GoTo ErrorHandler

    ' 随机生成RGB分量，确保亮度较高且饱和度高
    ' 高饱和度浅色：至少有一个分量接近最大值(255)，至少有一个分量较低
    Dim r As Integer, g As Integer, b As Integer
    Dim colorType As Integer

    ' 使用Rnd函数生成随机数
    Randomize ' 初始化随机数生成器

    ' 随机选择一种颜色类型（1-6，对应6种基本高饱和度浅色）
    colorType = Int(6 * Rnd + 1)

    Select Case colorType
        Case 1 ' 偏红色
            r = 255
            g = Int(100 * Rnd + 100) ' 100-199
            b = Int(100 * Rnd) ' 0-99
        Case 2 ' 偏绿色
            r = Int(100 * Rnd) ' 0-99
            g = 255
            b = Int(100 * Rnd + 100) ' 100-199
        Case 3 ' 偏蓝色
            r = Int(100 * Rnd + 100) ' 100-199
            g = Int(100 * Rnd) ' 0-99
            b = 255
        Case 4 ' 偏黄色
            r = 255
            g = 255
            b = Int(150 * Rnd) ' 0-149
        Case 5 ' 偏青色
            r = Int(150 * Rnd) ' 0-149
            g = 255
            b = 255
        Case 6 ' 偏品红色
            r = 255
            g = Int(150 * Rnd) ' 0-149
            b = 255
    End Select

    ' 返回RGB颜色值
    Dim colorValue As Long
    colorValue = RGB(r, g, b)

    ' 添加调试信息
    modDebug.LogVerbose "生成随机高饱和度浅色: R=" & r & ", G=" & g & ", B=" & b & ", RGB值=" & colorValue & ", 类型=" & colorType, _
        "modGanttSpotlight.GetRandomLightColor"

    GetRandomLightColor = colorValue
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGanttSpotlight.GetRandomLightColor"
    GetRandomLightColor = RGB(255, 255, 255) ' 出错时返回白色
End Function

' 生成随机高饱和度深色
Private Function GetRandomDarkColor() As Long
    On Error GoTo ErrorHandler

    ' 随机生成RGB分量，确保亮度较低且饱和度高
    ' 高饱和度深色：至少有一个分量接近中等值，其他分量较低
    Dim r As Integer, g As Integer, b As Integer
    Dim colorType As Integer

    ' 使用Rnd函数生成随机数
    Randomize ' 初始化随机数生成器

    ' 随机选择一种颜色类型（1-6，对应6种基本高饱和度深色）
    colorType = Int(6 * Rnd + 1)

    Select Case colorType
        Case 1 ' 深红色
            r = Int(50 * Rnd + 150) ' 150-199
            g = Int(50 * Rnd) ' 0-49
            b = Int(50 * Rnd) ' 0-49
        Case 2 ' 深绿色
            r = Int(50 * Rnd) ' 0-49
            g = Int(50 * Rnd + 150) ' 150-199
            b = Int(50 * Rnd) ' 0-49
        Case 3 ' 深蓝色
            r = Int(50 * Rnd) ' 0-49
            g = Int(50 * Rnd) ' 0-49
            b = Int(50 * Rnd + 150) ' 150-199
        Case 4 ' 深黄色
            r = Int(50 * Rnd + 150) ' 150-199
            g = Int(50 * Rnd + 150) ' 150-199
            b = Int(50 * Rnd) ' 0-49
        Case 5 ' 深青色
            r = Int(50 * Rnd) ' 0-49
            g = Int(50 * Rnd + 150) ' 150-199
            b = Int(50 * Rnd + 150) ' 150-199
        Case 6 ' 深品红色
            r = Int(50 * Rnd + 150) ' 150-199
            g = Int(50 * Rnd) ' 0-49
            b = Int(50 * Rnd + 150) ' 150-199
    End Select

    ' 返回RGB颜色值
    Dim colorValue As Long
    colorValue = RGB(r, g, b)

    ' 添加调试信息
    modDebug.LogVerbose "生成随机高饱和度深色: R=" & r & ", G=" & g & ", B=" & b & ", RGB值=" & colorValue & ", 类型=" & colorType, _
        "modGanttSpotlight.GetRandomDarkColor"

    GetRandomDarkColor = colorValue
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGanttSpotlight.GetRandomDarkColor"
    GetRandomDarkColor = RGB(0, 0, 0) ' 出错时返回黑色
End Function

