{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${input:github_token}"}, "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "."], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": []}}, "inputs": [{"type": "promptString", "id": "github_token", "description": "GitHub Personal Access Token", "password": true}]}