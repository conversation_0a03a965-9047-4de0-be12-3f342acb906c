# Project Management Gantt Chart System - 模块流程图

本文档使用Mermaid图表展示系统各个模块的流程和交互关系。

## 1. 系统整体流程

```mermaid
flowchart TD
    Start([开始]) --> Init[系统初始化]
    Init --> LoadConfig[加载配置]
    LoadConfig --> UserAction{用户操作}

    UserAction -->|编辑项目信息| EditProject[编辑项目信息]
    UserAction -->|编辑任务/里程碑| EditTasks[编辑任务/里程碑]
    UserAction -->|生成甘特图| GenerateGantt[生成甘特图]

    EditProject --> ValidateProject[验证项目数据]
    EditTasks --> ValidateTasks[验证任务数据]

    ValidateProject --> SaveProject[保存项目数据]
    ValidateTasks --> SaveTasks[保存任务数据]

    SaveProject --> UserAction
    SaveTasks --> UserAction

    GenerateGantt --> ValidateAllData[验证所有数据]
    ValidateAllData -->|数据有效| CreateGantt[创建甘特图]
    ValidateAllData -->|数据无效| ShowError[显示错误]

    CreateGantt --> UserAction
    ShowError --> UserAction

    UserAction -->|退出| End([结束])
```

## 2. Main模块流程

```mermaid
flowchart TD
    Start([开始]) --> GenerateGanttChart[生成甘特图主函数]
    GenerateGanttChart --> InitDebug[初始化调试模块]
    InitDebug --> DisableScreenUpdating[关闭屏幕更新]

    DisableScreenUpdating --> ValidateData{验证数据}
    ValidateData -->|验证失败| ShowError[显示错误消息]
    ValidateData -->|验证成功| PrepareGanttSheet[准备甘特图工作表]

    PrepareGanttSheet --> CreateGanttChart[创建甘特图]
    CreateGanttChart --> ApplyGanttTheme[应用甘特图主题]
    ApplyGanttTheme --> InitializeSpotlight[初始化聚光灯效果]

    InitializeSpotlight --> ShowSuccess[显示成功消息]
    ShowSuccess --> ActivateGanttSheet[激活甘特图工作表]

    ShowError --> CleanUp[清理资源]
    ActivateGanttSheet --> CleanUp

    CleanUp --> EnableScreenUpdating[恢复屏幕更新]
    EnableScreenUpdating --> CloseLogFile[关闭日志文件]
    CloseLogFile --> End([结束])
```

## 3. Data模块流程

### 3.1 数据验证流程

```mermaid
flowchart TD
    Start([开始]) --> ValidateAllData[验证所有数据]

    ValidateAllData --> ValidateProjectInfo{验证项目信息}
    ValidateProjectInfo -->|验证失败| ReturnFalse[返回False]
    ValidateProjectInfo -->|验证成功| ValidateTasksData{验证任务数据}

    ValidateTasksData -->|验证失败| ReturnFalse
    ValidateTasksData -->|验证成功| ReturnTrue[返回True]

    ReturnFalse --> End([结束])
    ReturnTrue --> End
```

### 3.2 配置访问流程

```mermaid
flowchart TD
    Start([开始]) --> GetConfigValue[获取配置值]

    GetConfigValue --> GetConfigTable[获取配置表引用]
    GetConfigTable --> CheckTableData{表格有数据?}

    CheckTableData -->|否| ReturnDefault[返回默认值]
    CheckTableData -->|是| FindConfigID[查找配置ID]

    FindConfigID --> ConfigFound{找到配置?}
    ConfigFound -->|否| ReturnDefault
    ConfigFound -->|是| ReturnValue[返回配置值]

    ReturnDefault --> End([结束])
    ReturnValue --> End
```

## 4. Gantt模块流程

### 4.1 甘特图生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateGanttChart[创建甘特图]
    CreateGanttChart --> LogFunctionEntry[记录函数进入]

    LogFunctionEntry --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> GetAllTasks[获取所有任务]

    GetAllTasks --> CreateTimeline[创建时间轴]
    CreateTimeline --> DrawTasksAndMilestones[绘制任务和里程碑]

    DrawTasksAndMilestones --> LogFunctionExit[记录函数退出]
    LogFunctionExit --> End([结束])

    GetProjectInfo -->|失败| LogError[记录错误]
    GetAllTasks -->|失败| LogError
    CreateTimeline -->|失败| LogError
    DrawTasksAndMilestones -->|失败| LogError

    LogError --> RaiseError[抛出错误]
    RaiseError --> End
```

### 4.2 时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateTimeline[创建时间轴]

    CreateTimeline --> CalculateTimeRange[计算时间范围]
    CalculateTimeRange --> GenerateYearRow[生成年份行]
    GenerateYearRow --> GenerateMonthRow[生成月份行]
    GenerateMonthRow --> GenerateWeekRow[生成周数行]

    GenerateWeekRow --> MergeCells[合并相同单元格]
    MergeCells --> FormatTimeline[格式化时间轴]

    FormatTimeline --> End([结束])
```

### 4.3 任务绘制流程

```mermaid
flowchart TD
    Start([开始]) --> DrawTasksAndMilestones[绘制任务和里程碑]
    DrawTasksAndMilestones --> LogFunctionEntry[记录函数进入]

    LogFunctionEntry --> CheckTasksCount{任务集合为空?}
    CheckTasksCount -->|是| LogWarning[记录警告并退出]
    CheckTasksCount -->|否| GetProjectInfo[获取项目信息]

    GetProjectInfo --> EstablishCoords[建立时间轴坐标系]
    EstablishCoords --> InitializeVariables[初始化变量]

    InitializeVariables --> ProcessTasks[处理每个任务]
    ProcessTasks --> DetermineTaskRow[确定任务行位置和类别]

    DetermineTaskRow --> CheckRowSuccess{行位置确定成功?}
    CheckRowSuccess -->|否| LogError[记录错误并退出]
    CheckRowSuccess -->|是| TaskType{任务类型?}

    TaskType -->|里程碑| DrawMilestone[绘制里程碑]
    TaskType -->|任务| DrawTask[绘制任务条]

    DrawMilestone --> AddTaskLabel[添加任务标签]
    DrawTask --> AddTaskLabel

    AddTaskLabel --> CheckBaseline{有基准线?}
    CheckBaseline -->|是| DrawBaseline[绘制基准线]
    CheckBaseline -->|否| MoreTasks{还有任务?}

    DrawBaseline --> MoreTasks
    MoreTasks -->|是| ProcessTasks
    MoreTasks -->|否| LogFunctionExit[记录函数退出]

    LogFunctionExit --> End([结束])
    LogWarning --> End
    LogError --> End
```

## 5. UI模块流程

### 5.1 甘特图工作表准备流程

```mermaid
flowchart TD
    Start([开始]) --> PrepareGanttWorksheet[准备甘特图工作表]
    PrepareGanttWorksheet --> LogFunctionEntry[记录函数进入]

    LogFunctionEntry --> DeleteGanttWorksheet[删除旧的甘特图工作表]
    DeleteGanttWorksheet --> CreateGanttWorksheet[创建新的甘特图工作表]

    CreateGanttWorksheet --> SetupHeaders[设置表头]
    SetupHeaders --> SetupGridlines[设置网格线]

    SetupGridlines --> LogFunctionExit[记录函数退出]
    LogFunctionExit --> End([结束])

    DeleteGanttWorksheet -->|失败| LogError[记录错误]
    CreateGanttWorksheet -->|失败| LogError
    SetupHeaders -->|失败| LogError
    SetupGridlines -->|失败| LogError

    LogError --> RaiseError[抛出错误]
    RaiseError --> End
```

### 5.2 时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> GenerateTimeline[生成时间轴]

    GenerateTimeline --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> GetConfig[获取配置]

    GetConfig --> DetermineTimeUnit[确定时间单位]
    DetermineTimeUnit --> SetupTimelineRows[设置年月周行位置]
    SetupTimelineRows --> SetOrigin[设置坐标原点]

    SetOrigin --> TimeUnitType{时间单位类型}
    TimeUnitType -->|日| GenerateDayView[生成日视图]
    TimeUnitType -->|周| GenerateWeekView[生成周视图]
    TimeUnitType -->|月| GenerateMonthView[生成月视图]

    GenerateDayView --> MergeYearCells[合并年份单元格]
    GenerateWeekView --> MergeYearCells
    GenerateMonthView --> MergeYearCells

    MergeYearCells --> MergeMonthCells[合并月份单元格]
    MergeMonthCells --> FormatTimeline[格式化时间轴]

    FormatTimeline --> MergeProjectInfoCells[合并项目信息单元格]
    MergeProjectInfoCells --> ReturnTimelineInfo[返回时间轴信息]

    ReturnTimelineInfo --> End([结束])
```

### 5.3 数据输入流程

```mermaid
flowchart TD
    Start([开始]) --> InputData[数据输入]

    InputData --> DataType{数据类型?}
    DataType -->|项目信息| InputProjectInfo[输入项目信息]
    DataType -->|任务/里程碑| InputTaskInfo[输入任务信息]

    InputProjectInfo --> ValidateInput[验证输入]
    InputTaskInfo --> ValidateInput

    ValidateInput --> IsValid{数据有效?}
    IsValid -->|否| ShowError[显示错误]
    IsValid -->|是| SaveData[保存数据]

    ShowError --> InputData
    SaveData --> End([结束])
```

## 6. 模块间交互流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Main as Main模块
    participant UI as UI模块
    participant Data as Data模块
    participant Gantt as Gantt模块

    User->>Main: 触发生成甘特图
    Main->>Data: 验证数据
    Data-->>Main: 返回验证结果

    alt 数据验证成功
        Main->>UI: 准备甘特图工作表
        UI->>Data: 获取项目信息
        Data-->>UI: 返回项目信息

        UI->>Data: 获取配置信息
        Data-->>UI: 返回配置信息

        UI->>UI: 生成时间轴
        UI-->>Main: 工作表准备完成

        Main->>Gantt: 创建甘特图
        Gantt->>Data: 获取项目信息
        Data-->>Gantt: 返回项目信息

        Gantt->>Data: 获取任务数据
        Data-->>Gantt: 返回任务数据

        Gantt->>Gantt: 绘制任务和里程碑

        Gantt-->>Main: 甘特图创建完成
        Main-->>User: 显示成功消息
    else 数据验证失败
        Main-->>User: 显示错误消息
    end
```

## 7. 配置访问流程

```mermaid
sequenceDiagram
    participant Module as 任意模块
    participant Data as Data模块
    participant Config as 配置工作表

    Module->>Data: 请求配置值(GetConfigValue)
    Data->>Config: 查找配置项

    alt 找到配置项
        Config-->>Data: 返回配置值
        Data-->>Module: 返回配置值
    else 未找到配置项
        Config-->>Data: 未找到
        Data-->>Module: 返回默认值
    end

    Module->>Data: 请求模块配置(GetModuleConfig)
    Data->>Config: 查找模块配置项
    Config-->>Data: 返回所有匹配配置
    Data-->>Module: 返回配置字典
```

## 8. 错误处理流程

```mermaid
flowchart TD
    Start([开始]) --> OnErrorGoTo[On Error GoTo ErrorHandler]

    OnErrorGoTo --> DisableScreenUpdating[关闭屏幕更新]
    DisableScreenUpdating --> InitializeDebug[初始化调试模块]
    InitializeDebug --> LogFunctionEntry[记录函数进入]

    LogFunctionEntry --> ExecuteCode[执行函数主体代码]
    ExecuteCode --> ErrorOccurred{发生错误?}

    ErrorOccurred -->|否| CleanUp[清理资源]
    ErrorOccurred -->|是| ErrorHandler[错误处理器]

    ErrorHandler --> LogError[记录错误信息]
    LogError --> DisplayMessage[显示用户友好消息]
    DisplayMessage --> ResumeCleanUp[Resume CleanUp]

    ResumeCleanUp --> CleanUp
    CleanUp --> EnableScreenUpdating[恢复屏幕更新]
    EnableScreenUpdating --> LogFunctionExit[记录函数退出]

    LogFunctionExit --> CloseLogFile{需要关闭日志?}
    CloseLogFile -->|是| CloseLog[关闭日志文件]
    CloseLogFile -->|否| End([结束])

    CloseLog --> End
```

### 8.1 调试日志流程

```mermaid
flowchart TD
    Start([开始]) --> LogFunction[调用日志函数]
    LogFunction --> IsDebugEnabled{调试模式启用?}

    IsDebugEnabled -->|否| End([结束])
    IsDebugEnabled -->|是| CheckLogLevel{检查日志级别}

    CheckLogLevel -->|级别不足| End
    CheckLogLevel -->|级别足够| FormatMessage[格式化消息]

    FormatMessage --> IsImmediateEnabled{即时窗口输出启用?}
    IsImmediateEnabled -->|是| OutputToImmediate[输出到即时窗口]
    IsImmediateEnabled -->|否| CheckFileLogging{文件日志启用?}

    OutputToImmediate --> CheckFileLogging
    CheckFileLogging -->|否| End
    CheckFileLogging -->|是| IsLogFileOpen{日志文件已打开?}

    IsLogFileOpen -->|否| OpenLogFile[打开日志文件]
    IsLogFileOpen -->|是| WriteToLogFile[写入日志文件]

    OpenLogFile --> WriteToLogFile
    WriteToLogFile --> End
```

## 9. 聚光灯效果流程

```mermaid
flowchart TD
    Start([开始]) --> InitializeGanttSpotlight[初始化甘特图聚光灯效果]
    InitializeGanttSpotlight --> LogFunctionEntry[记录函数进入]

    LogFunctionEntry --> GetSpotlightConfig[获取聚光灯配置]
    GetSpotlightConfig --> IsEnabled{聚光灯启用?}

    IsEnabled -->|否| LogInfo[记录信息并退出]
    IsEnabled -->|是| GetSpotlightMode[获取聚光灯模式]

    GetSpotlightMode --> GetColors[获取聚光灯颜色]
    GetColors --> ClearExistingFormatting[清除现有条件格式]

    ClearExistingFormatting --> CheckMode{检查模式}
    CheckMode -->|水平| AddHorizontalSpotlight[添加水平聚光灯]
    CheckMode -->|垂直| AddVerticalSpotlight[添加垂直聚光灯]
    CheckMode -->|全部| AddBothSpotlights[添加水平和垂直聚光灯]
    CheckMode -->|无| SkipSpotlight[跳过聚光灯]

    AddHorizontalSpotlight --> LogSuccess[记录成功]
    AddVerticalSpotlight --> LogSuccess
    AddBothSpotlights --> LogSuccess
    SkipSpotlight --> LogSuccess

    LogSuccess --> LogFunctionExit[记录函数退出]
    LogFunctionExit --> End([结束])
    LogInfo --> End
```

### 9.1 工作表选择变化处理流程

```mermaid
flowchart TD
    Start([开始]) --> SheetSelectionChange[工作表选择变化事件]
    SheetSelectionChange --> IsGanttSheet{是甘特图工作表?}

    IsGanttSheet -->|否| End([结束])
    IsGanttSheet -->|是| EnableScreenUpdating[启用屏幕更新]

    EnableScreenUpdating --> End
```
