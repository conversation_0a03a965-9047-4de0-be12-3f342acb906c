# Project Management Gantt Chart System - 系统架构设计

## 1. 系统架构概述

本系统采用模块化设计，基于Excel VBA开发。系统分为数据层、业务逻辑层和表现层三个主要部分，通过清晰的接口进行交互。系统还包含横切关注点（Cross-cutting Concerns）如调试、配置和错误处理，这些功能贯穿所有层次。

## 2. 系统架构图

```mermaid
flowchart TD
    subgraph 表现层
        UI[用户界面模块\nmodUI]
        Ribbon[Ribbon界面模块\nmodRibbon]
        ThisWB[ThisWorkbook类]
    end

    subgraph 业务逻辑层
        Main[主控模块\nmodMain]
        Gantt[甘特图模块\nmodGantt]
        GanttSpotlight[甘特图聚光灯模块\nmodGanttSpotlight]
    end

    subgraph 数据层
        Data[数据模块\nmodData]
        ConfigDefaults[配置默认值模块\nmodConfigDefaults]
    end

    subgraph 横切关注点
        Debug[调试模块\nmodDebug]
        Utilities[工具模块\nmodUtilities]
    end

    %% 表现层内部关系
    ThisWB --> UI
    Ribbon --> UI

    %% 表现层到业务逻辑层
    ThisWB --> Main
    Ribbon --> Main
    UI --> Gantt

    %% 业务逻辑层内部关系
    Main --> Gantt
    Main --> GanttSpotlight

    %% 业务逻辑层到数据层
    Main --> Data
    Gantt --> Data
    GanttSpotlight --> Data

    %% 数据层内部关系
    Data --> ConfigDefaults

    %% 横切关注点的关系
    Debug -.-> Main
    Debug -.-> UI
    Debug -.-> Gantt
    Debug -.-> Data
    Debug -.-> GanttSpotlight

    Utilities -.-> Main
    Utilities -.-> UI
    Utilities -.-> Gantt
    Utilities -.-> Data

    %% Excel对象模型
    Excel[Excel对象模型] --> ThisWB
    Excel --> UI
    Excel --> Gantt
    Excel --> Data
```

## 3. 模块说明

### 3.1 表现层

#### 3.1.1 用户界面模块 (modUI)
- **工作表准备**：准备甘特图工作表，设置格式和布局
- **表头设置**：设置甘特图表头和时间轴
- **网格线设置**：设置甘特图网格线
- **主题应用**：应用甘特图主题，包括颜色、字体和边框
- **配置预览**：应用配置表预览效果

#### 3.1.2 Ribbon界面模块 (modRibbon)
- **Ribbon按钮处理**：处理Ribbon界面上的按钮点击事件
- **调用主控模块**：调用主控模块的相应功能
- **错误处理**：处理Ribbon操作过程中的错误

#### 3.1.3 ThisWorkbook类
- **工作簿事件处理**：处理工作簿打开、关闭等事件
- **系统初始化**：初始化系统，包括调试模块和按钮创建
- **工作表选择变化处理**：处理工作表选择变化事件，支持聚光灯效果

### 3.2 业务逻辑层

#### 3.2.1 主控模块 (modMain)
- **甘特图生成流程控制**：控制甘特图生成的整体流程
- **数据验证**：验证项目信息和任务数据
- **工作表准备**：调用UI模块准备甘特图工作表
- **甘特图创建**：调用甘特图模块创建甘特图
- **主题应用**：调用UI模块应用甘特图主题
- **聚光灯效果初始化**：初始化甘特图聚光灯效果

#### 3.2.2 甘特图模块 (modGantt)
- **时间轴创建**：创建甘特图时间轴，包括年份、月份和周数
- **任务绘制**：绘制任务条和里程碑
- **标签添加**：添加任务和里程碑标签
- **基准线绘制**：绘制项目基准线
- **类别标题处理**：处理任务类别标题的合并和格式化

#### 3.2.3 甘特图聚光灯模块 (modGanttSpotlight)
- **聚光灯效果初始化**：初始化甘特图聚光灯效果
- **条件格式应用**：应用条件格式实现聚光灯效果
- **模式处理**：处理不同的聚光灯模式（水平、垂直、全部）
- **颜色处理**：处理聚光灯效果的颜色设置

### 3.3 数据层

#### 3.3.1 数据模块 (modData)
- **项目信息获取**：获取项目基本信息
- **任务数据获取**：获取所有任务和里程碑数据
- **数据验证**：验证项目信息和任务数据
- **配置访问**：访问系统配置，支持模块级和全局配置
- **数组优化**：使用数组优化数据访问，提高性能

#### 3.3.2 配置默认值模块 (modConfigDefaults)
- **默认配置提供**：提供所有配置项的默认值
- **模块分组**：按模块分组组织配置项
- **配置系统支持**：支持配置系统的初始化和回退

### 3.4 横切关注点

#### 3.4.1 调试模块 (modDebug)
- **日志记录**：记录系统运行过程中的各种信息
- **错误处理**：记录和处理系统错误
- **函数跟踪**：跟踪函数的进入和退出
- **性能监控**：监控系统性能
- **调试级别控制**：控制调试信息的详细程度

#### 3.4.2 工具模块 (modUtilities)
- **日期处理**：处理日期计算和格式化
- **单元格操作**：处理单元格合并和格式应用
- **颜色处理**：处理颜色转换和应用
- **字符串处理**：处理字符串格式化和验证
- **错误处理**：提供通用的错误处理函数

## 4. 工作表结构

### 4.1 项目信息工作表 (ProjectInfo)
存储项目基本信息，使用命名区域而非超级表：
- **projectName**：项目名称
- **projectManager**：项目经理
- **projectStartDate**：项目开始日期
- **projectEndDate**：项目结束日期
- **projectDescription**：项目描述

### 4.2 任务和里程碑工作表 (Milestones&WBS)
存储任务和里程碑数据，使用超级表 (ListObject) 名为 "taskTable"：
- **ID**：任务/里程碑的唯一标识符
- **Category**：所属类别
- **Description**：任务/里程碑描述
- **Type**：类型（A=任务活动，M=里程碑）
- **Start Date**：开始日期
- **End Date**：结束日期（里程碑只参照开始日期）
- **Duration**：持续时间（天）
- **Progress**：完成百分比（0-100%）
- **Position**：相对于上一行的位置（same/next/数字）
- **Color**：任务条/里程碑的填充颜色
- **Text Position**：文字相对于任务条/里程碑的位置
- **ShowDateInLabel**：是否在标签中显示日期（可选列）
- **Baseline**：基准线日期（可选列）

### 4.3 配置工作表 (Config)
存储系统配置，使用超级表 (ListObject) 名为 "configTable"：
- **Module**：配置所属模块
- **ConfigName**：配置名称
- **ConfigValue**：配置值
- **IsEnabled**：是否启用
- **Description**：配置说明

### 4.4 甘特图工作表 (GanttChart)
用于显示甘特图，动态生成：
- **A列**：任务ID
- **B列**：任务类别和描述
- **C列及以后**：时间轴和甘特图
- **第1-2行**：项目信息
- **第3行**：年份
- **第4行**：月份
- **第5行**：周数
- **第6行及以后**：任务和里程碑

## 5. VBA模块结构

### 5.1 主控模块 (modMain)
- **GenerateGanttChart()** - 甘特图生成主函数
- **ValidateData()** - 数据验证
- **PrepareGanttSheet()** - 准备甘特图工作表
- **CreateGanttChart()** - 创建甘特图
- **ApplyGanttTheme()** - 应用甘特图主题
- **InitializeGanttSpotlight()** - 初始化甘特图聚光灯效果
- **HandleError()** - 错误处理

### 5.2 甘特图模块 (modGantt)
- **CreateGanttChart()** - 创建甘特图
- **CreateTimeline()** - 创建时间轴
- **DrawTasksAndMilestones()** - 绘制任务和里程碑
- **DetermineTaskRowAndCategory()** - 确定任务行位置和类别
- **DrawTask()** - 绘制任务条
- **DrawMilestone()** - 绘制里程碑
- **AddTaskLabel()** - 添加任务标签
- **DrawBaseline()** - 绘制基准线
- **EstablishTimelineCoordinateSystem()** - 建立时间轴坐标系
- **CalculateXCoordinate()** - 计算X坐标

### 5.3 用户界面模块 (modUI)
- **PrepareGanttWorksheet()** - 准备甘特图工作表
- **DeleteGanttWorksheetIfExists()** - 删除已存在的甘特图工作表
- **CreateGanttWorksheet()** - 创建新的甘特图工作表
- **SetupHeaders()** - 设置表头
- **SetupGridlines()** - 设置网格线
- **ApplyGanttTheme()** - 应用甘特图主题
- **ApplyConfigTablePreview()** - 应用配置表预览

### 5.4 数据模块 (modData)
- **GetProjectInfo()** - 获取项目信息
- **GetAllTasks()** - 获取所有任务
- **ValidateAllData()** - 验证所有数据
- **ValidateTasksData()** - 验证任务数据
- **GetModuleConfig()** - 获取模块配置
- **GetConfig()** - 获取配置项
- **GetConfigFromDict()** - 从配置字典获取值

### 5.5 调试模块 (modDebug)
- **InitDebug()** - 初始化调试模块
- **LogError()** - 记录错误
- **LogWarning()** - 记录警告
- **LogInfo()** - 记录信息
- **LogVerbose()** - 记录详细信息
- **LogFunctionEntry()** - 记录函数进入
- **LogFunctionExit()** - 记录函数退出
- **OpenLogFile()** - 打开日志文件
- **CloseLogFile()** - 关闭日志文件

### 5.6 工具模块 (modUtilities)
- **CalculateWorkingDays()** - 计算工作日
- **GetWeekNumber()** - 获取周数
- **FormatDate()** - 格式化日期
- **MergeCells()** - 合并单元格
- **ApplyFormat()** - 应用格式
- **GetCellAddress()** - 获取单元格地址
- **IsEmptyOrNull()** - 检查是否为空
- **TrimAll()** - 去除所有空白
- **GetRGBColor()** - 获取RGB颜色

### 5.7 配置默认值模块 (modConfigDefaults)
- **GetDefaults()** - 获取默认配置值

### 5.8 Ribbon界面模块 (modRibbon)
- **Ribbon_GenerateGanttChart()** - 处理生成甘特图按钮点击
- **Ribbon_ApplyConfigPreview()** - 处理应用配置预览按钮点击

### 5.9 甘特图聚光灯模块 (modGanttSpotlight)
- **InitializeGanttSpotlight()** - 初始化甘特图聚光灯效果
- **ApplyHorizontalSpotlight()** - 应用水平聚光灯效果
- **ApplyVerticalSpotlight()** - 应用垂直聚光灯效果

## 6. 按钮和交互

### 6.1 工作表按钮
系统在 Milestones&WBS 工作表上创建以下按钮：
- **Generate Gantt Chart**：调用 modMain.GenerateGanttChart() 生成甘特图
- **Debug Tools**：调用 ShowDebugTools() 显示调试工具（仅在调试模式下可见）

### 6.2 Ribbon 界面
系统在 Excel Ribbon 界面上添加以下按钮：
- **Generate Gantt Chart**：调用 modRibbon.Ribbon_GenerateGanttChart() 生成甘特图
- **Apply Config Preview**：调用 modRibbon.Ribbon_ApplyConfigPreview() 应用配置预览

### 6.3 聚光灯效果
系统在甘特图工作表上实现聚光灯效果，通过以下方式实现：
- **水平聚光灯**：高亮显示当前选中行
- **垂直聚光灯**：高亮显示当前选中列
- **全部聚光灯**：同时高亮显示当前选中行和列

## 7. 数据流

### 7.1 甘特图生成数据流

```mermaid
sequenceDiagram
    participant User as 用户
    participant Ribbon as Ribbon界面
    participant Main as 主控模块
    participant Data as 数据模块
    participant UI as 用户界面模块
    participant Gantt as 甘特图模块
    participant Spotlight as 聚光灯模块
    participant Debug as 调试模块

    User->>Ribbon: 点击"Generate Gantt Chart"按钮
    Ribbon->>Main: 调用GenerateGanttChart()
    Main->>Debug: 初始化调试模块

    Main->>Data: 验证数据
    Data-->>Main: 返回验证结果

    alt 数据验证成功
        Main->>UI: 准备甘特图工作表
        UI->>UI: 删除旧工作表
        UI->>UI: 创建新工作表
        UI->>UI: 设置表头和网格线
        UI-->>Main: 工作表准备完成

        Main->>Gantt: 创建甘特图
        Gantt->>Data: 获取项目信息
        Data-->>Gantt: 返回项目信息
        Gantt->>Data: 获取任务数据
        Data-->>Gantt: 返回任务数据

        Gantt->>Gantt: 创建时间轴
        Gantt->>Gantt: 绘制任务和里程碑
        Gantt-->>Main: 甘特图创建完成

        Main->>UI: 应用甘特图主题
        UI-->>Main: 主题应用完成

        Main->>Spotlight: 初始化聚光灯效果
        Spotlight-->>Main: 聚光灯效果初始化完成

        Main->>User: 显示成功消息
    else 数据验证失败
        Main->>User: 显示错误消息
    end

    Main->>Debug: 关闭日志文件
```

### 7.2 配置访问数据流

```mermaid
sequenceDiagram
    participant Module as 任意模块
    participant Data as 数据模块
    participant Config as 配置工作表
    participant Defaults as 配置默认值模块

    Module->>Data: 请求配置值(GetConfig)
    Data->>Config: 查找配置项

    alt 找到配置项且已启用
        Config-->>Data: 返回配置值
        Data-->>Module: 返回配置值
    else 未找到配置项或未启用
        Config-->>Data: 未找到或未启用
        Data->>Defaults: 获取默认值
        Defaults-->>Data: 返回默认值
        Data-->>Module: 返回默认值
    end
```

## 8. 接口设计

### 8.1 模块间接口
各模块通过函数调用和事件触发进行交互，保持松耦合设计：
- **主控模块接口**：提供 GenerateGanttChart() 作为系统主入口
- **数据模块接口**：提供 GetProjectInfo()、GetAllTasks()、GetConfig() 等函数访问数据
- **甘特图模块接口**：提供 CreateGanttChart() 函数生成甘特图
- **UI模块接口**：提供 PrepareGanttWorksheet()、ApplyGanttTheme() 等函数处理界面
- **调试模块接口**：提供 LogError()、LogInfo() 等函数记录日志

### 8.2 数据结构接口
系统使用以下主要数据结构进行模块间数据传递：
- **Dictionary**：用于存储项目信息、任务数据和配置信息
- **Collection**：用于存储任务集合
- **Array**：用于批量处理数据，提高性能

### 8.3 配置接口
系统提供以下配置访问接口：
- **GetConfig(configName, defaultValue)**：获取单个配置项的值
- **GetModuleConfig(moduleName, defaultValues)**：获取特定模块的所有配置
- **GetConfigFromDict(dict, configName, defaultValue)**：从配置字典获取值

### 8.4 错误处理接口
系统提供以下错误处理接口：
- **LogError(errNumber, errDescription, errSource)**：记录错误信息
- **HandleError(errNumber, errDescription, errSource)**：处理错误并显示用户友好消息

## 9. 性能优化

### 9.1 数组批量处理
系统使用数组批量处理数据，避免频繁访问Excel对象模型：
- 一次性读取整个数据区域到数组
- 在内存中处理数据
- 一次性写回结果

### 9.2 屏幕更新控制
系统在处理过程中关闭屏幕更新，提高性能：
```vba
Application.ScreenUpdating = False
Application.EnableEvents = False
Application.Calculation = xlCalculationManual
' 处理代码
Application.ScreenUpdating = True
Application.EnableEvents = True
Application.Calculation = xlCalculationAutomatic
```

### 9.3 配置缓存
系统缓存频繁访问的配置值，避免重复查询：
```vba
' 一次性获取所有配置
Dim config As Dictionary
Set config = GetModuleConfig("Gantt")

' 使用缓存的配置
Dim cellWidthFactor As Double
cellWidthFactor = CDbl(GetConfigFromDict(config, "CellWidthFactor", 1.2))
```

## 10. 安全性和错误处理

### 10.1 数据验证
系统在处理数据前进行严格验证，确保数据有效：
- 验证项目日期的有效性
- 验证任务日期的有效性和一致性
- 验证必填字段的完整性

### 10.2 错误日志
系统使用多级日志记录错误和系统运行信息：
- **错误级别**：记录严重错误
- **警告级别**：记录潜在问题
- **信息级别**：记录一般信息
- **详细级别**：记录详细跟踪信息

### 10.3 用户反馈
系统提供清晰的用户反馈，帮助用户理解系统状态：
- 数据验证失败时显示具体错误信息
- 操作成功时显示确认消息
- 使用状态栏显示处理进度
