'Attribute VB_Name = "modRibbon"
Option Explicit

' =========================================================
' 模块: modRibbon
' 描述: 包含所有与Excel Ribbon界面相关的宏函数
' =========================================================

' ---------------------------------------------------------
' Ribbon回调函数
' ---------------------------------------------------------

' 生成甘特图
Public Sub Ribbon_GenerateGanttChart(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_GenerateGanttChart"

    ' 调用主函数生成甘特图
    modMain.GenerateGanttChart

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_GenerateGanttChart", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_GenerateGanttChart"
    MsgBox "生成甘特图时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 应用配置表预览
Public Sub Ribbon_ApplyConfigPreview(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_ApplyConfigPreview"

    ' 直接切换到Config工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("Config").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到Config工作表，无法应用配置预览。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到Config工作表", "modRibbon.Ribbon_ApplyConfigPreview"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用UI模块的函数应用配置预览
    modUI.ApplyConfigTablePreview

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_ApplyConfigPreview", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_ApplyConfigPreview"
    MsgBox "应用配置预览时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 合并跨月周单元格
Public Sub Ribbon_MergeWeekColumns(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_MergeWeekColumns"

    ' 直接切换到GanttChart工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("GanttChart").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到GanttChart工作表，无法执行合并操作。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到GanttChart工作表", "modRibbon.Ribbon_MergeWeekColumns"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用UI模块的函数合并跨月周单元格
    modUI.MergeWeekColumns

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_MergeWeekColumns", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_MergeWeekColumns"
    MsgBox "合并跨月周单元格时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 切换LabelText预览
Public Sub Ribbon_ToggleLabelTextPreview(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_ToggleLabelTextPreview"

    ' 直接切换到Milestones&WBS工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("Milestones&WBS").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到Milestones&WBS工作表，无法执行LabelText预览操作。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到Milestones&WBS工作表", "modRibbon.Ribbon_ToggleLabelTextPreview"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用切换函数
    ToggleLabelTextPreview

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_ToggleLabelTextPreview", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_ToggleLabelTextPreview"
    MsgBox "切换LabelText预览时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' ---------------------------------------------------------
' LabelText预览功能
' ---------------------------------------------------------

'*******************************************************************************
' Function Name: ToggleLabelTextPreview
' Purpose: Toggle between showing and clearing LabelText content in taskTable
' Parameters: None
' Returns: None
'*******************************************************************************
Private Sub ToggleLabelTextPreview()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.ToggleLabelTextPreview"

    Dim ws As Worksheet
    Dim tbl As ListObject
    Dim dataRange As Range
    Dim labelCol As Long
    Dim hasLabelText As Boolean
    Dim i As Long
    Dim currentState As String

    ' 获取工作表和表格
    Set ws = ThisWorkbook.Worksheets("Milestones&WBS")
    Set tbl = ws.ListObjects("taskTable")

    ' 检查LabelText列是否存在
    On Error Resume Next
    labelCol = tbl.ListColumns("LabelText").Index
    hasLabelText = (Err.Number = 0)
    On Error GoTo ErrorHandler

    If Not hasLabelText Then
        MsgBox "taskTable中未找到LabelText列，无法执行预览操作。", vbInformation, "提示"
        modDebug.LogWarning "taskTable中未找到LabelText列", "modRibbon.ToggleLabelTextPreview"
        Exit Sub
    End If

    ' 检查是否有数据
    If tbl.DataBodyRange Is Nothing Then
        MsgBox "taskTable中没有数据。", vbInformation, "提示"
        modDebug.LogInfo "taskTable中没有数据", "modRibbon.ToggleLabelTextPreview"
        Exit Sub
    End If

    Set dataRange = tbl.DataBodyRange

    ' 检查当前状态：如果LabelText列有内容，则清空；如果为空，则生成预览
    currentState = DetermineLabelTextState(dataRange, labelCol)

    If currentState = "HAS_CONTENT" Then
        ' 清空LabelText内容
        ClearLabelTextContent dataRange, labelCol
        MsgBox "已清空LabelText内容，恢复表格可读性。", vbInformation, "LabelText预览"
        modDebug.LogInfo "已清空LabelText内容", "modRibbon.ToggleLabelTextPreview"
    Else
        ' 生成LabelText预览
        If GenerateLabelTextPreview() Then
            MsgBox "已生成LabelText预览内容。", vbInformation, "LabelText预览"
            modDebug.LogInfo "已生成LabelText预览内容", "modRibbon.ToggleLabelTextPreview"
        Else
            MsgBox "生成LabelText预览失败，请检查数据。", vbExclamation, "错误"
            modDebug.LogWarning "生成LabelText预览失败", "modRibbon.ToggleLabelTextPreview"
        End If
    End If

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.ToggleLabelTextPreview", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.ToggleLabelTextPreview"
    MsgBox "切换LabelText预览时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 判断LabelText列的当前状态
Private Function DetermineLabelTextState(dataRange As Range, labelCol As Long) As String
    Dim i As Long
    Dim cellValue As String

    ' 检查前几行是否有内容来判断状态
    For i = 1 To Application.WorksheetFunction.Min(dataRange.Rows.Count, 5)
        cellValue = Trim(CStr(dataRange.Cells(i, labelCol).Value))
        If cellValue <> "" Then
            DetermineLabelTextState = "HAS_CONTENT"
            Exit Function
        End If
    Next i

    DetermineLabelTextState = "EMPTY"
End Function

' 清空LabelText内容 - 使用Range操作优化性能
Private Sub ClearLabelTextContent(dataRange As Range, labelCol As Long)
    Dim rowCount As Long
    Dim labelRange As Range

    modDebug.LogInfo "开始清空LabelText内容", "modRibbon.ClearLabelTextContent"

    rowCount = dataRange.Rows.Count

    ' 获取LabelText列的范围并一次性清空
    Set labelRange = dataRange.Columns(labelCol).Resize(rowCount, 1)
    labelRange.ClearContents

    modDebug.LogInfo "LabelText内容清空完成，共处理 " & rowCount & " 行", "modRibbon.ClearLabelTextContent"
End Sub

' 生成LabelText预览（调用现有的GenerateTaskLabels函数）
Private Function GenerateLabelTextPreview() As Boolean
    On Error GoTo ErrorHandler

    modDebug.LogInfo "开始生成LabelText预览", "modRibbon.GenerateLabelTextPreview"

    ' 调用modData中的GenerateTaskLabels函数
    GenerateLabelTextPreview = modData.GenerateTaskLabels()

    If GenerateLabelTextPreview Then
        modDebug.LogInfo "LabelText预览生成成功", "modRibbon.GenerateLabelTextPreview"
    Else
        modDebug.LogWarning "LabelText预览生成失败", "modRibbon.GenerateLabelTextPreview"
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.GenerateLabelTextPreview"
    GenerateLabelTextPreview = False
End Function

' ---------------------------------------------------------
' 辅助函数
' ---------------------------------------------------------

' 获取Ribbon XML
Public Function GetRibbonXML() As String
    ' 这个函数可以返回动态生成的Ribbon XML
    ' 目前返回一个基本的XML结构，可以根据需要扩展

    Dim xml As String

    ' 分段构建XML以避免"too many line continuations"错误
    xml = "<customUI xmlns='http://schemas.microsoft.com/office/2009/07/customui'>"
    xml = xml & "  <ribbon>"
    xml = xml & "    <tabs>"
    xml = xml & "      <tab id='tabGanttChart' label='甘特图'>"

    ' 甘特图工具组
    xml = xml & "        <group id='grpGantt' label='甘特图工具'>"
    xml = xml & "          <button id='btnGenerateGantt' label='生成甘特图' "
    xml = xml & "                  imageMso='ChartInsert' size='large' "
    xml = xml & "                  onAction='modRibbon.Ribbon_GenerateGanttChart'/>"
    xml = xml & "          <button id='btnMergeWeekColumns' label='合并跨月周' "
    xml = xml & "                  imageMso='MergeCells' size='large' "
    xml = xml & "                  onAction='modRibbon.Ribbon_MergeWeekColumns'/>"
    xml = xml & "        </group>"

    ' 配置工具组
    xml = xml & "        <group id='grpConfig' label='配置工具'>"
    xml = xml & "          <button id='btnApplyConfigPreview' label='应用配置预览' "
    xml = xml & "                  imageMso='FormattingProperties' size='large' "
    xml = xml & "                  onAction='modRibbon.Ribbon_ApplyConfigPreview'/>"
    xml = xml & "        </group>"

    ' 标签工具组
    xml = xml & "        <group id='grpLabelTools' label='标签工具'>"
    xml = xml & "          <button id='btnToggleLabelTextPreview' label='切换标签预览' "
    xml = xml & "                  imageMso='ShowDetail' size='large' "
    xml = xml & "                  onAction='modRibbon.Ribbon_ToggleLabelTextPreview'/>"
    xml = xml & "        </group>"

    ' 结束标签
    xml = xml & "      </tab>"
    xml = xml & "    </tabs>"
    xml = xml & "  </ribbon>"
    xml = xml & "</customUI>"

    GetRibbonXML = xml
End Function
