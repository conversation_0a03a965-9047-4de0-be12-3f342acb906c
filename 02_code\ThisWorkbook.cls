' VERSION 1.0 CLASS
' BEGIN
'   MultiUse = -1  'True
' END
' Attribute VB_Name = "ThisWorkbook"
' Attribute VB_GlobalNameSpace = False
' Attribute VB_Creatable = False
' Attribute VB_PredeclaredId = True
' Attribute VB_Exposed = True
Option Explicit

' 工作表选择变化事件
Private Sub Workbook_SheetSelectionChange(ByVal Sh As Object, ByVal Target As Range)
    On Error Resume Next

    ' 只处理甘特图工作表
    If Sh.Name = "GanttChart" Then
        ' 强制刷新屏幕，使条件格式生效
        Application.ScreenUpdating = True
    End If
End Sub