Sub GenerateCalendarOff()
    '
    ' 生成非工作时间表 - 核心思路说明：
    ' =====================================
    ' 目标：生成指定日期范围内所有的非工作时间
    ' 公式：非工作时间 = (所有周六周日 + 法定节假日) - 补班日期
    '
    ' 处理逻辑：
    ' 1. 遍历日期范围内每一天
    ' 2. 判断是否为周末(周六/周日)或法定节假日
    ' 3. 排除补班日期(即使是周末也要上班)
    ' 4. 日期唯一性：同一天既是周末又是法定节假日时，优先保留法定节假日备注
    '
    ' 性能优化：
    ' - 使用Dictionary快速查找节假日和补班日期
    ' - 使用数组收集所有数据，最后批量写入工作表
    ' - 使用Range.Resize一次性更新多行数据
    '

    Dim ws As Worksheet
    Dim startDate As Date, endDate As Date
    Dim holidayDict As Object, makeupDict As Object
    Dim dateKey As String

    ' 设置目标工作表
    Set ws = ThisWorkbook.Worksheets("Calendar")

    ' 设置处理的日期范围
    startDate = DateValue("2022/1/1")
    endDate = DateValue("2025/12/31")

    ' 步骤1：创建字典用于快速查找
    Set holidayDict = CreateObject("Scripting.Dictionary")  ' 存储法定节假日：日期->备注
    Set makeupDict = CreateObject("Scripting.Dictionary")   ' 存储补班日期：日期->True

    ' 步骤2：从Holiday超级表加载法定节假日数据
    Dim holidayTable As ListObject
    Set holidayTable = FindListObject(ws, "Holiday")
    If holidayTable Is Nothing Then
        MsgBox "在Calendar工作表中找不到Holiday超级表，请检查表格是否存在。"
        Exit Sub
    End If

    ' 使用数组批量读取节假日数据，提升性能
    Dim holidayData As Variant
    If holidayTable.DataBodyRange Is Nothing Then
        MsgBox "Holiday超级表中没有数据。"
        Exit Sub
    End If
    holidayData = holidayTable.DataBodyRange.Value
    Dim i As Long
    For i = 1 To UBound(holidayData, 1)
        If holidayData(i, 3) <> "" Then ' 第3列：节假日日期
            dateKey = Format(holidayData(i, 3), "yyyy/m/d")
            holidayDict(dateKey) = holidayData(i, 5) ' 第5列：节假日备注
        End If
    Next i

    ' 步骤3：从make_up超级表加载补班日期数据
    Dim makeupTable As ListObject
    Set makeupTable = FindListObject(ws, "make_up")
    If makeupTable Is Nothing Then
        MsgBox "在Calendar工作表中找不到make_up超级表，请检查表格是否存在。"
        Exit Sub
    End If

    ' 使用数组批量读取补班数据
    Dim makeupData As Variant
    If makeupTable.DataBodyRange Is Nothing Then
        MsgBox "make_up超级表中没有数据。"
        Exit Sub
    End If
    makeupData = makeupTable.DataBodyRange.Value
    For i = 1 To UBound(makeupData, 1)
        If makeupData(i, 3) <> "" Then ' 第3列：补班日期
            dateKey = Format(makeupData(i, 3), "yyyy/m/d")
            makeupDict(dateKey) = True ' 标记为补班日期
        End If
    Next i

    ' 步骤4：定位calendar_off超级表
    Dim calendarOffTable As ListObject
    Set calendarOffTable = FindListObject(ws, "calendar_off")
    If calendarOffTable Is Nothing Then
        MsgBox "在Calendar工作表中找不到calendar_off超级表，请检查表格是否存在。"
        Exit Sub
    End If

    ' 步骤5：预估最大记录数并创建数组（4年约1460天，周末+节假日约占30%）
    Dim maxRecords As Long
    maxRecords = DateDiff("d", startDate, endDate) * 0.3 + 100

    ' 创建数组用于批量处理，避免逐个单元格操作
    Dim dateArray() As Date
    Dim remarkArray() As String
    ReDim dateArray(1 To maxRecords)
    ReDim remarkArray(1 To maxRecords)

    ' 步骤6：核心算法 - 遍历日期范围，筛选非工作时间
    Dim currentDate As Date
    Dim recordCount As Long
    Dim isWeekend As Boolean, isHoliday As Boolean, isMakeup As Boolean
    Dim remark As String

    recordCount = 0
    currentDate = startDate

    Do While currentDate <= endDate
        dateKey = Format(currentDate, "yyyy/m/d")

        ' 判断当前日期的性质
        isWeekend = (Weekday(currentDate) = 1 Or Weekday(currentDate) = 7) ' 周日=1, 周六=7
        isHoliday = holidayDict.Exists(dateKey)  ' 是否为法定节假日
        isMakeup = makeupDict.Exists(dateKey)    ' 是否为补班日期

        ' 核心逻辑：(周末 OR 节假日) AND NOT 补班日期
        If (isWeekend Or isHoliday) And Not isMakeup Then
            recordCount = recordCount + 1

            ' 存储到数组中，优先保留节假日备注
            dateArray(recordCount) = currentDate
            If isHoliday Then
                remarkArray(recordCount) = holidayDict(dateKey) ' 法定节假日备注
            Else
                remarkArray(recordCount) = "" ' 普通周末无备注
            End If
        End If

        currentDate = currentDate + 1
    Loop

    ' 步骤7：清空calendar_off超级表现有数据（只清空Weekend+Holiday和Remark两列）
    If Not calendarOffTable.DataBodyRange Is Nothing Then
        ' 清空Weekend+Holiday列（第3列）
        calendarOffTable.DataBodyRange.Columns(3).ClearContents
        ' 清空Remark列（第5列）
        calendarOffTable.DataBodyRange.Columns(5).ClearContents
    End If

    ' 步骤8：使用超级表批量写入数据，高效率处理
    If recordCount > 0 Then
        ' 调整数组大小到实际记录数
        ReDim Preserve dateArray(1 To recordCount)
        ReDim Preserve remarkArray(1 To recordCount)

        ' 确保超级表有足够的行数
        Dim currentRows As Long
        If calendarOffTable.DataBodyRange Is Nothing Then
            currentRows = 0
        Else
            currentRows = calendarOffTable.DataBodyRange.Rows.Count
        End If

        ' 如果需要更多行，则添加行
        If recordCount > currentRows Then
            calendarOffTable.Resize calendarOffTable.Range.Resize(recordCount + 1, calendarOffTable.Range.Columns.Count)
        End If

        ' 批量写入日期到Weekend+Holiday列（第3列）
        calendarOffTable.DataBodyRange.Columns(3).Resize(recordCount, 1).Value = Application.Transpose(dateArray)

        ' 批量写入备注到Remark列（第5列）
        calendarOffTable.DataBodyRange.Columns(5).Resize(recordCount, 1).Value = Application.Transpose(remarkArray)
    End If

    ' 步骤9：清理内存
    Set holidayDict = Nothing
    Set makeupDict = Nothing

    MsgBox "非工作时间表生成完成！共生成 " & recordCount & " 条记录。"

End Sub

' 辅助函数：查找超级表(ListObject)
Function FindListObject(ws As Worksheet, tableName As String) As ListObject
    Dim tbl As ListObject

    ' 方法1：直接通过名称查找
    On Error Resume Next
    Set tbl = ws.ListObjects(tableName)
    On Error GoTo 0

    If Not tbl Is Nothing Then
        Set FindListObject = tbl
        Exit Function
    End If

    ' 方法2：通过名称模糊匹配查找
    For Each tbl In ws.ListObjects
        If InStr(1, tbl.Name, tableName, vbTextCompare) > 0 Then
            Set FindListObject = tbl
            Exit Function
        End If
    Next tbl

    ' 方法3：通过表格标题查找（检查第一行是否包含表名）
    For Each tbl In ws.ListObjects
        If Not tbl.HeaderRowRange Is Nothing Then
            Dim cell As Range
            For Each cell In tbl.HeaderRowRange
                If InStr(1, cell.Value, tableName, vbTextCompare) > 0 Then
                    Set FindListObject = tbl
                    Exit Function
                End If
            Next cell
        End If
    Next tbl

    ' 未找到
    Set FindListObject = Nothing
End Function
