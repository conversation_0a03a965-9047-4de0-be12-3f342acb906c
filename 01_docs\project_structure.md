# Project Management Gantt Chart System - 项目结构

```
Project Management Gantt Chart System/
│
├── 00_workbook/                    # 包含主要Excel文件，开发中的版本
│   └── Project Management Gantt Chart v0.xlsm  # 主要Excel文件
│
├── 01_docs/                        # 文档目录
│   ├── design/                     # 设计文档
│   │   ├── architecture.md          # 系统架构设计
│   │   ├── v1_system_design.md       # 系统设计文档
│   │   ├── modData_design.md         # Data模块设计
│   │   ├── modGantt_design.md        # Gantt模块设计
│   │   ├── modMain_design.md         # Main模块设计
│   │   ├── modUI_design.md           # UI模块设计
│   │   └── modUtilities_design.md    # Utilities模块设计
│   ├── module_flow_diagrams.md      # 模块流程图
│   ├── project_structure.md         # 项目结构文档
│   ├── table_data_access.md         # 表格数据访问示例
│   └── task_fields_updated.md      # 任务字段定义
│
├── 02_code/                        # VBA代码目录
│   ├── modData.bas                  # 数据模块
│   ├── modDebug.bas                 # 调试模块
│   ├── modDebugTools.bas            # 调试工具模块
│   ├── modGantt.bas                 # 甘特图模块
│   ├── modMain.bas                  # 主模块
│   ├── modUI.bas                    # 界面模块
│   ├── modUtilities.bas             # 工具模块
│   └── ThisWorkbook.cls             # 工作簿类
│
├── assets/                         # 静态资源
│
└── dist/                           # 发布文件夹（用于发布/打包版本）
```

## 模块说明

### 1. modMain 模块
系统的入口点和控制中心，负责协调其他模块的工作，处理主要事件和错误。

主要功能：
- 生成甘特图的主函数
- 错误处理

### 2. modData 模块
负责数据处理、验证和管理，为其他模块提供数据服务。

主要功能：
- 数据验证
- 项目信息获取
- 任务数据获取
- 配置管理

### 3. modGantt 模块
负责甘特图的生成和管理，是系统的核心功能模块。

主要功能：
- 创建时间轴
- 绘制任务和里程碑
- 应用格式和样式

### 4. modUI 模块
负责用户界面交互和显示，处理工作表的准备、格式化和用户交互。

主要功能：
- 准备甘特图工作表
- 生成时间轴和确定坐标原点
- 设置工作表格式
- 设置表头
- 应用工作表保护

### 5. modUtilities 模块
提供通用工具函数，为其他模块提供支持。

主要功能：
- 日期处理
- 单元格操作
- 辅助函数

### 6. modDebug 模块
提供调试和日志功能，帮助跟踪和诊断问题。

主要功能：
- 日志记录
- 错误处理
- 调试信息输出
- 性能监控

### 7. modDebugTools 模块
提供调试工具菜单和功能，用于辅助开发和测试过程。

主要功能：
- 显示调试工具菜单
- 切换调试级别
- 测试时间轴生成
- 显示项目信息和任务数据

### 8. ThisWorkbook 类
处理工作簿事件，包括工作簿打开时初始化系统。

主要功能：
- 工作簿打开事件处理
- 系统初始化

## 工作表说明

### 1. ProjectInfo 工作表
存储项目基本信息，使用定义名称引用数据区域。

主要字段：
- 项目名称 (projectName)
- 项目经理 (projectManager)
- 开始日期 (projectStartDate)
- 结束日期 (projectEndDate)
- 项目描述 (projectDescription)

### 2. Milestones&WBS 工作表
存储任务和里程碑数据，使用超级表(taskTable)组织数据。

主要字段：
- ID (taskId)
- Category (taskCategory)
- Description (taskDescription)
- Type (taskType)
- Start Date (taskStartDate)
- End Date (taskEndDate)
- Duration (taskDuration)
- Progress (taskProgress)
- Position (taskPosition)
- Color (taskColor)
- Text Position (taskTextPosition)

### 3. Config 工作表
存储系统配置参数，使用超级表(configTable)组织数据。

主要字段：
- ConfigID
- ConfigName
- ConfigValue
- Description
- Module
- Category
- IsEnabled

### 4. GanttChart 工作表
显示甘特图，包含时间轴和任务/里程碑可视化。在运行时生成。时间轴支持日/周/月三种不同的时间单位视图，并且在生成时会确定坐标原点，作为绘制任务和里程碑的基准。
