
Sub CreateGanttChartHeader_weeknum()
    Dim ws As Worksheet
    Dim startDate As Date, endDate As Date, pj_startDate As Date, pj_endDate As Date
    Dim switchDate As Date
    Dim headerRange As Range
    Dim lastMonth As Integer, lastYear As Integer
    Dim monthStart As Range, yearStart As Range
    Dim col_Width_factor As Single

    ' Check if "Plan" sheet already exists and delete it if found
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Plan" Then
            Application.DisplayAlerts = False ' Suppress the delete confirmation message
            ws.Delete
            Application.DisplayAlerts = True
            Exit For
        End If
    Next ws

    ' Create a new sheet named "Plan"
    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ws.Name = "Plan"

    ' Set all cells to have white background
    ws.Cells.Interior.Color = RGB(255, 255, 255)
    ' 设置所有单元格的文字居中对齐
ws.Cells.HorizontalAlignment = xlCenter

    ' Set the header origin point at B2
    Set headerRange = ws.Range("B1")

    ' Set project start and end dates
    pj_startDate = DateValue("12/21/2023") ' 示例日期
    pj_endDate = DateValue("2/1/2024")   ' 示例日期
    ' 设置列宽的调整系数，用于让列宽与天数挂钩
    col_Width_factor = 1

    ' Set the start month and end month based on the given start and end dates
    startDate = DateSerial(Year(pj_startDate), Month(pj_startDate), 1)
    endDate = DateSerial(Year(pj_endDate), Month(pj_endDate) + 1, 0)

    ' Write the calendar weeks and corresponding dates in rows 6 and 5, respectively
    switchDate = startDate + 1 - Weekday(startDate, vbMonday) '初始化设置为起始日所在日历周的第一天
    Do While switchDate <= endDate
        With headerRange


            If Month(switchDate) <> Month(switchDate + 6) Then '跨月判断
            '跨月-第一次运行
                If (Month(switchDate) Mod 12) + 1 = Month(startDate) And switchDate < startDate Then
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate + 6, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate + 6)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate + 6)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "the first blood in CW " & WorksheetFunction.WeekNum(switchDate + 6, 2) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                End If
            '最后一周
                If switchDate = endDate - Weekday(endDate, vbMonday) + 1 Then
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                .Offset(2, 0).Value = Year(switchDate)
                .EntireColumn.ColumnWidth = Weekday(endDate, vbMonday) * col_Width_factor
                Debug.Print "the last blood in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & Weekday(endDate, vbMonday)
                End If
            '中间周
                If switchDate > startDate And switchDate < endDate - Weekday(endDate, vbMonday) + 1 Then
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"
                .Offset(4, 0).EntireColumn.ColumnWidth = Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) * col_Width_factor
                .Offset(2, 0).Value = Year(switchDate)
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate + 6, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate + 6)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate + 6)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "Mix-killing_A in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)
                Debug.Print "Mix-killing_B in CW " & WorksheetFunction.WeekNum(switchDate + 6, 2) & ", DAYS: " & (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday))
 
                End If
            ElseIf switchDate = startDate Then
            '不跨月，第一次运行
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "the first blood in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)



            Else
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                .Offset(2, 0).Value = Year(switchDate)
                .EntireColumn.ColumnWidth = 7 * col_Width_factor
                Debug.Print "Perfect-shot in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: 7"
            End If

        End With


        ' Move to the next column
        Set headerRange = headerRange.Offset(0, 1)
        switchDate = switchDate + 7 ' Move to the next week
    Loop
    Dim lastCol As Long
    Dim i As Integer, j As Integer
    Dim mergeRange As Range

    ' 确定最后一列
    lastCol = ws.Cells(4, ws.Columns.Count).End(xlToLeft).Column

    ' 关闭警告消息
    Application.DisplayAlerts = False

    ' 遍历每一行
    For i = 3 To 5
        Set mergeRange = Nothing
        For j = 3 To lastCol
            If Not mergeRange Is Nothing And ws.Cells(i, j).Value = ws.Cells(i, j - 1).Value Then
                ' 扩展当前的合并范围
                Set mergeRange = ws.Range(mergeRange, ws.Cells(i, j))
            Else
                ' 如果当前单元格与前一个单元格的值不同
                If Not mergeRange Is Nothing Then
                    ' 合并之前的范围
                    If mergeRange.Columns.Count > 1 Then
                        mergeRange.Merge
                    End If
                End If
                ' 重置合并范围
                Set mergeRange = ws.Cells(i, j)
            End If
        Next j
        ' 检查并合并最后的范围
        If Not mergeRange Is Nothing And mergeRange.Columns.Count > 1 Then
            mergeRange.Merge
        End If
    Next i

    ' 重新开启警告消息
    Application.DisplayAlerts = True
    
   
End Sub




Sub CreateGanttChartHeader_weeknum_debug()
    Dim ws As Worksheet
    Dim startDate As Date, endDate As Date, pj_startDate As Date, pj_endDate As Date
    Dim switchDate As Date
    Dim headerRange As Range
    Dim lastMonth As Integer, lastYear As Integer
    Dim monthStart As Range, yearStart As Range
    Dim col_Width_factor As Single

    ' Check if "Plan" sheet already exists and delete it if found
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Plan" Then
            Application.DisplayAlerts = False ' Suppress the delete confirmation message
            ws.Delete
            Application.DisplayAlerts = True
            Exit For
        End If
    Next ws

    ' Create a new sheet named "Plan"
    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ws.Name = "Plan"

    ' Set all cells to have white background
    ws.Cells.Interior.Color = RGB(255, 255, 255)
    ' 设置所有单元格的文字居中对齐
ws.Cells.HorizontalAlignment = xlCenter

    ' Set the header origin point at B2
    Set headerRange = ws.Range("B1")

    ' Set project start and end dates
    pj_startDate = DateValue("12/21/2023") ' 示例日期
    pj_endDate = DateValue("2/1/2024")   ' 示例日期
    ' 设置列宽的调整系数，用于让列宽与天数挂钩
    col_Width_factor = 1

    ' Set the start month and end month based on the given start and end dates
    startDate = DateSerial(Year(pj_startDate), Month(pj_startDate), 1)
    endDate = DateSerial(Year(pj_endDate), Month(pj_endDate) + 1, 0)

    ' Write the calendar weeks and corresponding dates in rows 6 and 5, respectively
    switchDate = startDate + 1 - Weekday(startDate, vbMonday) '初始化设置为起始日所在日历周的第一天
    Do While switchDate <= endDate
        With headerRange


            If Month(switchDate) <> Month(switchDate + 6) Then '跨月判断
            '跨月-第一次运行
                If (Month(switchDate) Mod 12) + 1 = Month(startDate) And switchDate < startDate Then
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate + 6, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate + 6)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate + 6)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "the first blood in CW " & WorksheetFunction.WeekNum(switchDate + 6, 2) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                End If
            '最后一周
                If switchDate = endDate - Weekday(endDate, vbMonday) + 1 Then
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                .Offset(2, 0).Value = Year(switchDate)
                .EntireColumn.ColumnWidth = Weekday(endDate, vbMonday) * col_Width_factor
                Debug.Print "the last blood in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & Weekday(endDate, vbMonday)
                End If
            '中间周
                If switchDate > startDate And switchDate < endDate - Weekday(endDate, vbMonday) + 1 Then
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"
                .Offset(4, 0).EntireColumn.ColumnWidth = Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) * col_Width_factor
                .Offset(2, 0).Value = Year(switchDate)
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate + 6, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate + 6)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate + 6)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "Mix-killing_A in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)
                Debug.Print "Mix-killing_B in CW " & WorksheetFunction.WeekNum(switchDate + 6, 2) & ", DAYS: " & (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday))
 
                End If
            ElseIf switchDate = startDate Then
            '不跨月，第一次运行
                .Offset(4, 1).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 1).NumberFormat = "00"
                .Offset(4, 1).HorizontalAlignment = xlCenter

                .Offset(3, 1).Value = Month(switchDate)
                .Offset(3, 1).NumberFormat = "00"

                .Offset(2, 1).Value = Year(switchDate)
                .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                Set headerRange = headerRange.Offset(0, 1)
                Debug.Print "the first blood in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)



            Else
                .Offset(4, 0).Value = WorksheetFunction.WeekNum(switchDate, 2)
                .Offset(4, 0).NumberFormat = "00"
                .Offset(4, 0).HorizontalAlignment = xlCenter

                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                .Offset(2, 0).Value = Year(switchDate)
                .EntireColumn.ColumnWidth = 7 * col_Width_factor
                Debug.Print "Perfect-shot in CW " & WorksheetFunction.WeekNum(switchDate, 2) & ", DAYS: 7"
            End If

        End With


        ' Move to the next column
        Set headerRange = headerRange.Offset(0, 1)
        switchDate = switchDate + 7 ' Move to the next week
    Loop
    Dim lastCol As Long
    Dim i As Integer, j As Integer
    Dim mergeRange As Range

    ' 确定最后一列
    lastCol = ws.Cells(4, ws.Columns.Count).End(xlToLeft).Column

    ' 关闭警告消息
    Application.DisplayAlerts = False

    ' 遍历每一行
    For i = 3 To 5
        Set mergeRange = Nothing
        For j = 3 To lastCol
            If Not mergeRange Is Nothing And ws.Cells(i, j).Value = ws.Cells(i, j - 1).Value Then
                ' 扩展当前的合并范围
                Set mergeRange = ws.Range(mergeRange, ws.Cells(i, j))
            Else
                ' 如果当前单元格与前一个单元格的值不同
                If Not mergeRange Is Nothing Then
                    ' 合并之前的范围
                    If mergeRange.Columns.Count > 1 Then
                        mergeRange.Merge
                    End If
                End If
                ' 重置合并范围
                Set mergeRange = ws.Cells(i, j)
            End If
        Next j
        ' 检查并合并最后的范围
        If Not mergeRange Is Nothing And mergeRange.Columns.Count > 1 Then
            mergeRange.Merge
        End If
    Next i

    ' 重新开启警告消息
    Application.DisplayAlerts = True
    
   
End Sub









