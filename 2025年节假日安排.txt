2025年节假日安排

节假日：名称"Holiday"
Month	CW	Holiday	Weekday	Remark
1	1	2025/1/1	星期三	元旦
1	5	2025/1/28	星期二	春节
1	5	2025/1/29	星期三	春节
1	5	2025/1/30	星期四	春节
1	5	2025/1/31	星期五	春节
2	5	2025/2/1	星期六	春节
2	5	2025/2/2	星期日	春节
2	6	2025/2/3	星期一	春节
2	6	2025/2/4	星期二	春节
4	14	2025/4/4	星期五	清明节
4	14	2025/4/5	星期六	清明节
4	14	2025/4/6	星期日	清明节
5	18	2025/5/1	星期四	劳动节
5	18	2025/5/2	星期五	劳动节
5	18	2025/5/3	星期六	劳动节
5	18	2025/5/4	星期日	劳动节
5	19	2025/5/5	星期一	劳动节
5	22	2025/5/31	星期六	端午节
6	22	2025/6/1	星期日	端午节
6	23	2025/6/2	星期一	端午节
10	40	2025/10/1	星期三	国庆节
10	40	2025/10/2	星期四	国庆节
10	40	2025/10/3	星期五	国庆节
10	40	2025/10/4	星期六	国庆节
10	40	2025/10/5	星期日	国庆节
10	41	2025/10/6	星期一	国庆节
10	41	2025/10/7	星期二	国庆节
10	41	2025/10/8	星期三	国庆节

补班：名称”make_up“
Month	CW	make_up workday	Weekday	Remark
1	4	2025/1/26	星期日	春节补班
2	6	2025/2/8	星期六	春节补班
4	17	2025/4/27	星期日	劳动节补班
9	39	2025/9/28	星期日	国庆节补班
10	41	2025/10/11	星期六	国庆节补班

基于上面两个表，手动维护的，以及硬编码的指定的起始日期2022/1/1，结束日期2025/12/31，生成一个包含设置范围内所有周末、节假日，不包含补班的表，格式如下，这三个超级表都在一个工作表中(Calendar),这里面日期不要重复，如果重复的话优先保留节假日的，注意calendar_off表里面
只用更新Weekend+Holiday和Remark两列即可，其他的有公式自动填充，不要你操作：

需要更新的表： calendar_off
Month	CW	Weekend+Holiday	Weekday	Remark
1	1	2025/1/4	星期六	
1	1	2025/1/5	星期日	
1	2	2025/1/11	星期六	
1	2	2025/1/12	星期日	
1	3	2025/1/18	星期六	
1	3	2025/1/19	星期日	
1	4	2025/1/25	星期六	
1	4	2025/1/26	星期日	


