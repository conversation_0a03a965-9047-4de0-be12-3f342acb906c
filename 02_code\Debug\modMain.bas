
'Attribute VB_Name = "modMain"
Option Explicit

' =========================================================
' Module: modMain
' Description: System entry point and control center
' =========================================================

' ---------------------------------------------------------
' Main Functions
' ---------------------------------------------------------

' Main function to generate Gantt chart
Public Sub GenerateGanttChart()
    On Error GoTo ErrorHandler

    ' 直接获取Debug模块配置参数
    Dim debugLevel As Integer
    Dim enableDebug As Boolean
    Dim enableFileLogging As Boolean
    Dim enableImmediateOutput As Boolean

    ' 获取配置值
    debugLevel = CInt(GetConfig("DebugLevel", 4))
    enableDebug = CBool(GetConfig("EnableDebug", False))
    enableFileLogging = CBool(GetConfig("EnableFileLogging", False))
    enableImmediateOutput = CBool(GetConfig("EnableImmediateOutput", False))

    ' 初始化调试系统，使用配置值
    modDebug.InitDebug _
        level:=debugLevel, _
        enableDebug:=enableDebug, _
        enableFileLogging:=enableFileLogging, _
        enableImmediateOutput:=enableImmediateOutput

    ' 重新初始化日志文件
    If modDebug.IsDebugEnabled And modDebug.IsFileLoggingEnabled Then
        modDebug.LogInfo "开始重新初始化日志文件", "modMain.GenerateGanttChart"
        modDebug.ReinitializeLog
    End If

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.GenerateGanttChart"
    modDebug.LogInfo "调试级别: " & modDebug.DebugLevel, "modMain.GenerateGanttChart"

    ' 显示状态栏消息
    Application.StatusBar = "正在生成甘特图..."
    modDebug.LogInfo "设置状态栏: 正在生成甘特图...", "modMain.GenerateGanttChart"

    ' 关闭屏幕更新以提高性能
    modDebug.LogInfo "关闭屏幕更新和事件", "modMain.GenerateGanttChart"
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    Application.Calculation = xlCalculationManual

    ' 1. 验证数据
    modDebug.LogInfo "步骤1: 开始验证数据", "modMain.GenerateGanttChart"
    If Not ValidateData() Then
        modDebug.LogWarning "数据验证失败", "modMain.GenerateGanttChart"
        MsgBox "数据验证失败。请检查项目信息和任务数据。", vbExclamation, "验证错误"
        GoTo CleanUp
    End If
    modDebug.LogInfo "数据验证成功", "modMain.GenerateGanttChart"

    ' 2. 准备甘特图工作表
    modDebug.LogInfo "步骤2: 开始准备甘特图工作表", "modMain.GenerateGanttChart"
    PrepareGanttSheet
    modDebug.LogInfo "甘特图工作表准备完成", "modMain.GenerateGanttChart"

    ' 3. 创建甘特图
    modDebug.LogInfo "步骤3: 开始创建甘特图", "modMain.GenerateGanttChart"
    CreateGanttChart
    modDebug.LogInfo "甘特图创建完成", "modMain.GenerateGanttChart"

    ' 4. 应用甘特图主题
    modDebug.LogInfo "步骤4: 开始应用甘特图主题", "modMain.GenerateGanttChart"
    ApplyGanttTheme
    modDebug.LogInfo "甘特图主题应用完成", "modMain.GenerateGanttChart"

    ' 5. 初始化甘特图聚光灯效果
    modDebug.LogInfo "步骤5: 初始化甘特图聚光灯效果", "modMain.GenerateGanttChart"
    InitializeGanttSpotlight
    modDebug.LogInfo "甘特图聚光灯效果初始化完成", "modMain.GenerateGanttChart"

    ' 6. 显示成功消息
    modDebug.LogInfo "步骤6: 显示成功消息", "modMain.GenerateGanttChart"
    MsgBox "甘特图生成成功！", vbInformation, "成功"

    ' 7. 激活甘特图工作表
    modDebug.LogInfo "步骤7: 激活甘特图工作表", "modMain.GenerateGanttChart"
    ThisWorkbook.Worksheets("GanttChart").Activate

CleanUp:
    ' 恢复屏幕更新
    modDebug.LogInfo "恢复屏幕更新和事件", "modMain.GenerateGanttChart"
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    Application.Calculation = xlCalculationAutomatic
    Application.StatusBar = False

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.GenerateGanttChart"

    ' 如果debug模式开启且文件日志启用，关闭日志文件
    If modDebug.IsDebugEnabled And modDebug.IsFileLoggingEnabled Then
        modDebug.LogInfo "任务正常完成", "modMain.GenerateGanttChart"
        modDebug.CloseLogFile
    End If

    Exit Sub

ErrorHandler:
    ' 处理错误
    modDebug.LogError Err.Number, Err.Description, "modMain.GenerateGanttChart"

    ' 如果debug模式开启且文件日志启用，关闭日志文件
    If modDebug.IsDebugEnabled And modDebug.IsFileLoggingEnabled Then
        modDebug.LogInfo "任务异常中断: " & Err.Number & " - " & Err.Description, "modMain.GenerateGanttChart"
        modDebug.CloseLogFile
    End If

    HandleError Err.Number, Err.Description, "modMain.GenerateGanttChart"
    Resume CleanUp
End Sub

' ---------------------------------------------------------
' Helper Functions
' ---------------------------------------------------------

' Validate data
Private Function ValidateData() As Boolean
    On Error GoTo ErrorHandler

    ' Log function entry
    modDebug.LogFunctionEntry "modMain.ValidateData"

    ' Call validation function from Data module
    ValidateData = modData.ValidateAllData()

    ' Log result
    modDebug.LogFunctionExit "modMain.ValidateData", "Result: " & ValidateData
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.ValidateData"
    ValidateData = False
    modDebug.LogFunctionExit "modMain.ValidateData", "Error result: False"
End Function

' Prepare Gantt chart worksheet
Private Sub PrepareGanttSheet()
    On Error GoTo ErrorHandler

    ' Log function entry
    modDebug.LogFunctionEntry "modMain.PrepareGanttSheet"

    ' Call preparation function from UI module
    modUI.PrepareGanttWorksheet

    ' Log function exit
    modDebug.LogFunctionExit "modMain.PrepareGanttSheet"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.PrepareGanttSheet"
    Err.Raise Err.Number, "modMain.PrepareGanttSheet", Err.Description
End Sub

' Create Gantt chart
Private Sub CreateGanttChart()
    On Error GoTo ErrorHandler

    ' Log function entry
    modDebug.LogFunctionEntry "modMain.CreateGanttChart"

    ' Call creation function from Gantt module
    modGantt.CreateGanttChart

    ' Log function exit
    modDebug.LogFunctionExit "modMain.CreateGanttChart"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.CreateGanttChart"
    Err.Raise Err.Number, "modMain.CreateGanttChart", Err.Description
End Sub

' 应用甘特图主题
Private Sub ApplyGanttTheme()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.ApplyGanttTheme"

    ' 获取甘特图工作表
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 调用UI模块的主题应用函数
    modUI.ApplyGanttTheme ws

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.ApplyGanttTheme"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.ApplyGanttTheme"
    Err.Raise Err.Number, "modMain.ApplyGanttTheme", Err.Description
End Sub

' 初始化甘特图聚光灯效果
Private Sub InitializeGanttSpotlight()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.InitializeGanttSpotlight"

    ' 获取甘特图工作表
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 调用聚光灯模块的初始化函数
    modGanttSpotlight.InitializeGanttSpotlight ws

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.InitializeGanttSpotlight"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.InitializeGanttSpotlight"
    Err.Raise Err.Number, "modMain.InitializeGanttSpotlight", Err.Description
End Sub

' ---------------------------------------------------------
' Error Handling
' ---------------------------------------------------------

' Handle error
Public Sub HandleError(errNumber As Long, errDescription As String, errSource As String)
    ' Log error
    modDebug.LogError errNumber, errDescription, errSource

    ' Log additional information if available
    If errNumber = 9 Then
        modDebug.LogVerbose "This may be a Dictionary object reference error. Check if all required Dictionary objects are properly initialized.", "ErrorAnalysis"
    End If

    ' Display user-friendly message
    MsgBox "Error occurred: " & errDescription, vbExclamation, "Error"
End Sub
