'Attribute VB_Name = "modUtilities"
Option Explicit

' =========================================================
' 模块: modUtilities
' 描述: 提供通用工具函数，为其他模块提供支持
' =========================================================



' ---------------------------------------------------------
' 日期处理函数
' ---------------------------------------------------------

' 计算两个日期之间的工作日数量
Public Function CalculateWorkingDays(startDate As Date, endDate As Date, Optional excludeWeekends As Boolean = True) As Long
    On Error GoTo ErrorHandler

    Dim days As Long
    Dim currentDate As Date

    days = 0
    currentDate = startDate

    ' 遍历每一天
    Do While currentDate <= endDate
        ' 检查是否为工作日
        If Not excludeWeekends Or (Weekday(currentDate) <> vbSaturday And Weekday(currentDate) <> vbSunday) Then
            days = days + 1
        End If

        ' 下一天
        currentDate = currentDate + 1
    Loop

    CalculateWorkingDays = days
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.CalculateWorkingDays"
    CalculateWorkingDays = 0 ' 返回默认值
End Function

' 获取日期的周数
Public Function GetWeekNumber(inputDate As Date) As String
    On Error GoTo ErrorHandler

    Dim weekNum As Integer

    ' 使用ISO 8601标准计算周数
    weekNum = DatePart("ww", inputDate, vbMonday, vbFirstFourDays)

    ' 格式化为"cwXX"
    GetWeekNumber = "cw" & Format(weekNum, "00")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetWeekNumber"
    GetWeekNumber = "cw00" ' 返回默认值
End Function

' 格式化日期为指定格式的字符串
Public Function FormatDate(inputDate As Date, Optional formatString As String = "yyyy-mm-dd") As String
    On Error GoTo ErrorHandler

    FormatDate = Format(inputDate, formatString)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.FormatDate"
    FormatDate = "" ' 返回默认值
End Function

' ---------------------------------------------------------
' 单元格操作函数
' ---------------------------------------------------------

' 合并指定范围的单元格
Public Sub MergeCells(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ws.Range(rangeAddress).Merge
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.MergeCells"
End Sub

' 应用格式到指定范围
Public Sub ApplyFormat(range As Range, formatParams As Dictionary)
    On Error GoTo ErrorHandler

    ' 设置字体
    If formatParams.Exists("FontName") Then
        range.Font.Name = formatParams("FontName")
    End If

    If formatParams.Exists("FontSize") Then
        range.Font.Size = formatParams("FontSize")
    End If

    If formatParams.Exists("FontBold") Then
        range.Font.Bold = formatParams("FontBold")
    End If

    ' 设置颜色
    If formatParams.Exists("FontColor") Then
        range.Font.Color = formatParams("FontColor")
    End If

    If formatParams.Exists("BackColor") Then
        range.Interior.Color = formatParams("BackColor")
    End If

    ' 设置对齐
    If formatParams.Exists("HAlign") Then
        range.HorizontalAlignment = formatParams("HAlign")
    End If

    If formatParams.Exists("VAlign") Then
        range.VerticalAlignment = formatParams("VAlign")
    End If

    ' 设置边框
    If formatParams.Exists("BorderWeight") Then
        range.BorderAround Weight:=formatParams("BorderWeight")
    End If

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.ApplyFormat"
End Sub

' 获取指定行列的单元格地址
Public Function GetCellAddress(ws As Worksheet, row As Long, col As Long) As String
    On Error GoTo ErrorHandler

    GetCellAddress = ws.Cells(row, col).Address
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetCellAddress"
    GetCellAddress = "" ' 返回默认值
End Function

' ---------------------------------------------------------
' 错误处理函数
' ---------------------------------------------------------

' 记录错误信息到日志 (转发到modDebug.LogError)
Public Sub LogError(errNumber As Long, errDescription As String, errSource As String)
    On Error Resume Next ' 避免日志记录本身出错

    ' 直接调用modDebug模块的LogError函数
    modDebug.LogError errNumber, errDescription, errSource
End Sub

' 检查值是否在指定范围内
Public Function IsInRange(value As Variant, minValue As Variant, maxValue As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsInRange = (value >= minValue And value <= maxValue)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsInRange"
    IsInRange = False ' 返回默认值
End Function

' ---------------------------------------------------------
' 其他工具函数
' ---------------------------------------------------------

' 检查值是否为空或Null
Public Function IsEmptyOrNull(value As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsEmptyOrNull = (IsEmpty(value) Or IsNull(value) Or value = "")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsEmptyOrNull"
    IsEmptyOrNull = True ' 返回默认值
End Function

' 去除字符串中的所有空白字符
Public Function TrimAll(text As String) As String
    On Error GoTo ErrorHandler

    TrimAll = Application.WorksheetFunction.Trim(text)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.TrimAll"
    TrimAll = "" ' 返回默认值
End Function

' 生成唯一ID
Public Function GetUniqueID(Optional prefix As String = "") As String
    On Error GoTo ErrorHandler

    Dim guid As String
    guid = Mid(CreateObject("Scriptlet.TypeLib").GUID, 2, 36)

    If prefix <> "" Then
        GetUniqueID = prefix & "_" & guid
    Else
        GetUniqueID = guid
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetUniqueID"
    GetUniqueID = Format(Now, "yyyymmddhhmmss") ' 返回基于时间的ID作为备选
End Function

' 获取RGB颜色值
Public Function GetRGBColor(colorHex As String) As Long
    On Error GoTo ErrorHandler

    Dim r As Long, g As Long, b As Long

    ' 移除可能的#前缀
    If Left(colorHex, 1) = "#" Then
        colorHex = Mid(colorHex, 2)
    End If

    ' 解析十六进制颜色值
    r = CLng("&H" & Mid(colorHex, 1, 2))
    g = CLng("&H" & Mid(colorHex, 3, 2))
    b = CLng("&H" & Mid(colorHex, 5, 2))

    ' 返回RGB值
    GetRGBColor = RGB(r, g, b)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetRGBColor"
    GetRGBColor = RGB(0, 0, 0) ' 返回黑色作为默认值
End Function






