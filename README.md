# Project Management Gantt Chart System

## 1. 项目概述

这个基于 Excel VBA 的项目管理系统允许您为项目创建和管理甘特图。它提供了项目计划的可视化表示，包括任务、里程碑和它们的依赖关系，帮助项目经理和团队成员更好地规划、跟踪和管理项目进度。

## 2. 功能特性

### 2.1 第一版本功能

- **项目信息管理**：在专用超级表模板上更新和管理项目基本信息
- **任务与里程碑管理**：在专用超级表中添加和管理任务与里程碑，包括：
  - 开始/结束日期设置
  - 文字描述
  - 所属大类(Category)分类
  - 可视化属性设置（形状、位置、颜色、文字内容及位置等）
- **甘特图可视化**：自动生成项目甘特图，直观展示项目时间线和进度

## 3. 系统架构

### 3.1 工作表结构

- **项目信息工作表**：默认超级表模板，存储项目基本信息
- **任务/里程碑工作表**：默认超级表模板，存储所有任务和里程碑数据
- **甘特图工作表**：每次生成时重新创建，展示可视化甘特图
- **配置工作表**：存储系统配置参数，用于后续扩展开发（如UI属性等）

### 3.2 技术组件

- **VBA模块**：
  - Main：主控模块，系统入口
  - Gantt：甘特图生成和管理
  - UI：用户界面交互
  - Data：数据处理和验证
- **开发方式**：前期开发直接从Excel开发面板调试，暂不需要用户窗体

## 4. 使用指南

### 4.1 安装

1. 打开Excel文件 `Project Management Gantt Chart v0.xlsm`
2. 根据提示启用宏功能
3. 基于用户指令响应

### 4.2 创建新项目

1. 在项目信息工作表中填写项目基本信息（名称、经理、日期等）
2. 在任务/里程碑工作表中添加项目任务和里程碑：
   - 填写文字说明和所属大类
   - 设置在甘特图中的位置、颜色、文字内容及位置等属性
   - 里程碑信息通常集中在表格上方，任务信息在下方
3. 触发"生成甘特图"指令，系统将：
   - 检查数据有效性
   - 按照要求生成甘特图

### 4.3 数据字段说明

#### 4.3.1 项目信息

- **项目名称**：项目的名称
- **项目经理**：负责项目的经理姓名
- **开始日期**：项目的开始日期
- **结束日期**：项目的结束日期
- **描述**：项目的简要描述

#### 4.3.2 任务/里程碑

- **类型**：所属父任务的大类，如"项目启动"
- **属性**：A/M（A:activity活动，M:milestone里程碑）
- **描述**：任务/里程碑的描述
- **ID**：任务/里程碑的唯一标识符
- **开始日期**：任务开始的时间
- **结束日期**：任务结束的时间（里程碑只参照开始日期）
- **持续时间**：自动计算（天）
- **进度**：完成百分比（0-100%）
- **依赖关系**：必须首先完成的任务ID
- **分配给**：负责任务的人员
- **颜色**：任务条/里程碑的填充颜色
- **位置**：相对于上一行的位置，可设置：
  - same：与上一行处于同一行
  - next：在下一行
  - 数字：以上一行为基准的相对行数（可为正负数）
- **文字位置**：
  - 里程碑：上、下、左、右四个选项
  - 任务条：上、下、左、右、中间五个选项

### 4.4 甘特图规范

甘特图提供以下内容的可视化表示：

- **任务**：显示为水平条
- **里程碑**：显示为菱形
- **进度**：显示为任务条的彩色部分
- **时间表**：基于项目日期的时间轴

#### 4.4.1 甘特图格式规范

- 创建前检查并删除旧的甘特图工作表
- 表头格式：
  - B1：第一行大字显示项目名称
  - B2：第二行小字显示项目经理信息
  - B3-B5：合并单元格显示最近更新时间及计划版本
- 时间轴格式：
  - C3行起：年份(yyyy)
  - C4行起：月份(mm)
  - C5行起：周数(cwxx)，基于ISO 8601标准
  - 相邻相等的单元格需合并处理
- B6行起：显示任务/里程碑所属大类，使用粗体

## 5. 最佳实践

1. 将项目分解为可管理的任务
2. 设置合理的开始和结束日期
3. 定期更新任务进度
4. 使用里程碑标记关键项目节点
5. 明确分配任务责任人

## 6. 故障排除

### 6.1 常见问题

- 出现错误时，检查报错代码行及具体报错信息
- 甘特图未更新时，点击"生成甘特图"按钮重新生成
- 日期显示不正确时，检查日期格式是否正确
- 系统异常时，尝试重启Excel并重试操作

### 6.2 Dictionary对象错误

**问题描述**：错误提示"用户定义类型未定义"

**原因**：这个错误通常是因为代码中使用了Dictionary对象，但是没有引用Microsoft Scripting Runtime库导致的。

**解决方法**：

1. 在VBA编辑器中，点击菜单栏的"工具"(Tools)
2. 选择"引用"(References)
3. 在弹出的对话框中，找到并勾选"Microsoft Scripting Runtime"
4. 点击"确定"保存设置

### 6.3 模块导入顺序

如果在导入VBA代码时出现错误，请确保按照以下顺序导入：

1. 首先导入modUtilities.bas
2. 然后导入modData.bas
3. 接着导入modUI.bas
4. 然后导入modGantt.bas
5. 最后导入modMain.bas和ThisWorkbook.cls

导入顺序很重要，因为后面的模块依赖于前面的模块。

### 6.4 二义性错误

**问题描述**：错误提示“发现二义性的名称LogError”

**原因**：这个错误通常是因为在多个模块中定义了相同名称的函数，或者在调用函数时没有指定模块名称。

**解决方法**：

1. 在调用函数时明确指定模块名称，例如：
   ```vba
   ' 修改前
   LogError Err.Number, Err.Description, "modMain.ValidateData"

   ' 修改后
   modUtilities.LogError Err.Number, Err.Description, "modMain.ValidateData"
   ```

2. 确保只在modUtilities模块中定义了LogError函数，如果在其他模块中也定义了同名函数，请将其重命名或删除。

### 6.5 子过程或函数未定义

**问题描述**：错误提示“子过程或函数未定义”

**原因**：这个错误通常是因为代码中调用了一个不存在的函数，或者没有指定函数所在的模块名称。

**解决方法**：

1. 在调用函数时明确指定模块名称，例如：
   ```vba
   ' 修改前
   ValidateData = ValidateAllData()

   ' 修改后
   ValidateData = modData.ValidateAllData()
   ```

2. 确保所有模块已经正确导入，并且按照正确的顺序导入。

3. 检查函数名称的拼写是否正确，确保没有拼写错误。

### 6.6 Office 应用的 DPI 感知设置

**问题描述**：在高DPI显示器或多显示器环境下，VBA UserForms 可能出现图像偏移、界面元素位置错误或显示异常的问题。

**原因**：较新版本的 Office (如 Microsoft 365, Office 2016/2019) 对 DPI 缩放的处理比旧版本好得多，但在某些情况下仍可能出现兼容性问题。

**解决方法**：

1. 打开 Excel 应用程序
2. 点击"文件"菜单
3. 选择"选项"
4. 在左侧菜单中选择"常规"
5. 找到"用户界面选项"部分
6. 将 Office 的 DPI 感知设置改为"**为兼容性优化 (Optimize for compatibility)**"
7. 点击"确定"保存设置
8. 重启 Excel 应用程序使设置生效

**说明**：
- 此设置影响整个 Office 应用程序的 DPI 处理方式
- "为兼容性优化"模式可以解决 VBA UserForms 在不同 DPI 显示器上的图像偏移问题
- 设置后可能会影响 Office 界面的清晰度，但可以提高 VBA 应用程序的兼容性
- 如果您的系统没有出现 DPI 相关问题，建议保持默认设置

## 7. 联系方式

如需技术支持或提出建议，请联系开发人员。
