# 甘特图标签处理流程说明文档

本文档详细说明甘特图系统中标签处理的输入、输出、流程、实现细节和函数调用逻辑。

## 目录

1. [概述](#概述)
2. [标签处理函数](#标签处理函数)
3. [输入参数](#输入参数)
4. [处理流程](#处理流程)
5. [标签位置算法](#标签位置算法)
6. [输出结果](#输出结果)
7. [函数调用逻辑](#函数调用逻辑)
8. [流程图](#流程图)
9. [注意事项](#注意事项)

## 概述

甘特图系统中的标签处理是通过 `AddTaskLabelWithCoordinates2` 函数实现的。该函数负责为任务条（矩形）和里程碑（菱形）创建描述标签，并根据配置或自动判断将标签放置在合适的位置。标签的位置可以通过任务的 `TextPosition` 属性进行配置。

## 标签处理相关函数

标签处理涉及以下几个关键函数，它们共同协作完成标签的创建、定位和调整：

### 1. AddTaskLabelWithCoordinates2 函数

这是标签处理的核心函数，负责创建和定位标签。该函数支持可配置的标签距离和智能对齐。

#### 函数签名

```vba
Private Function AddTaskLabelWithCoordinates2(task As Dictionary, shape As Shape, centerX As Double, centerY As Double, shapeSize As Double, labelDistance As Long) As Shape
```

#### 函数职责

- 判断形状类型（里程碑或任务条）
- 获取标签文本和位置设置
- 计算标签尺寸和位置
- 创建标签文本框并设置格式
- 将标签与形状组合在一起
- 返回创建的标签对象

#### 实现细节

- 使用 `shape.AutoShapeType = msoShapeDiamond` 判断形状是否为里程碑
- 通过临时文本框计算文本尺寸
- 根据形状类型和TextPosition属性计算标签位置
- 使用 `ws.Shapes.Range(Array(shape.Name, labelShape.Name)).Group` 组合形状和标签
- 详细记录处理过程，便于调试

### 2. AdjustRowHeightWithPadding 函数

这个函数负责调整行高以适应任务条/里程碑和标签，并确保它们垂直居中于行。

#### 函数签名

```vba
Private Sub AdjustRowHeightWithPadding(ws As Worksheet, row As Long, shapeObj As Shape, labelObj As Shape, padding As Long)
```

#### 函数职责

- 计算形状和标签占用的总高度
- 根据需要调整行高
- 确保形状和标签垂直居中于行
- 处理工作表保护问题

#### 实现细节

- 首先检查形状是否已经组合在一起（通过检查`shapeObj.ParentGroup`）
- 如果形状已组合，直接使用组的尺寸计算所需高度
- 如果形状未组合，分别计算形状和标签的边界，找出最高点和最低点
- 计算所需行高 = 最低点 - 最高点 + (padding * 2)
- 如果所需行高大于当前行高，则调整行高
- 计算行中心和形状组合中心，计算需要移动的距离
- 根据形状是否已组合，移动整个组或分别移动形状和标签
- 确保形状和标签垂直居中于行，提高视觉美观性

## 输入参数

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含任务或里程碑信息的字典对象 |
| shape | Shape | 任务条或里程碑形状对象 |
| centerX | Double | 形状中心点的X坐标 |
| centerY | Double | 形状中心点的Y坐标 |
| shapeSize | Double | 形状的大小（高度） |
| labelDistance | Long | 标签与形状之间的距离（像素） |

### 任务字典中的关键属性

| 属性 | 说明 | 默认值 |
|------|------|--------|
| Description | 标签显示的文本内容 | 无（必需） |
| TextPosition | 标签位置（"left"、"right"、"top"、"bottom"、"inside"） | 任务："right"，里程碑："right" |
| Type | 任务类型（"A"=任务条，"M"=里程碑） | 无（必需） |

## 处理流程

标签处理的主要流程包括以下步骤：

1. **获取任务描述**
   - 从任务字典中获取描述文本
   - 如果描述为空，则不创建标签

2. **判断形状类型**
   - 检测形状是里程碑（菱形）还是任务条（矩形）
   - 根据形状类型选择不同的标签位置计算逻辑

3. **计算文本尺寸**
   - 创建临时文本框以计算文本的实际尺寸
   - 设置文本框边距，使标签框尺寸更小

4. **确定标签位置和对齐方式**
   - 获取指定的 `TextPosition` 属性
   - 对于任务条：支持上、下、左、右、内部五种位置
   - 对于里程碑：支持上、下、左、右四种位置
   - 对于任务条的inside位置，先检查标签是否能放在形状内部，如果不能则改为right位置
   - 根据不同位置计算标签坐标和文本对齐方式

5. **创建标签文本框**
   - 使用计算好的位置和尺寸创建文本框
   - 设置文本内容、字体、大小和对齐方式
   - 设置文本框透明（无填充和边框）

6. **组合形状和标签**
   - 将标签和任务条/里程碑组合在一起
   - 对于任务条，如果有进度条，也将其包含在组合中
   - 计算组合的整体高度
   - 调整行高并使组合垂直居中于行

## 标签位置算法

标签位置的确定是标签处理中最复杂的部分，主要包括以下几种情况：

### 1. 内部位置（仅适用于任务条）

对于任务条，如果标签宽度小于任务条宽度的90%且指定为"inside"，则放在任务条内部，居中对齐：

```vba
' 检查是否可以放在内部（仅适用于任务条）
Dim canFitInside As Boolean
canFitInside = (textWidth <= taskWidth * 0.9) And textPosition = "inside"

' 如果指定为inside但放不下，改为right
If textPosition = "inside" And Not canFitInside Then
    textPosition = "right"
    modDebug.LogVerbose "任务条标签放不下inside位置，改为right", "modGantt.AddTaskLabelWithCoordinates2"
End If

Select Case textPosition
    Case "inside"
        ' 放在任务条内部，居中对齐
        labelX = centerX - textWidth / 2  ' 水平居中对齐
        labelY = centerY - textHeight / 2
        textAlign = xlCenter
        modDebug.LogVerbose "任务条标签放置在内部，居中对齐", "modGantt.AddTaskLabelWithCoordinates2"
    ' ... 其他位置处理 ...
End Select
```

### 2. 右侧位置（"right"）

```vba
' 放在右侧，左对齐
If isMilestone Then
    ' 里程碑是菱形，使用实际宽度
    labelX = centerX + shape.Width / 2 + labelDistance
Else
    labelX = centerX + taskWidth / 2 + labelDistance
End If
labelY = centerY - textHeight / 2  ' 水平中心与任务条/里程碑中心水平对齐
textAlign = xlLeft
```

### 3. 左侧位置（"left"）

```vba
' 放在左侧，右对齐
If isMilestone Then
    ' 里程碑是菱形，使用实际宽度
    labelX = centerX - shape.Width / 2 - labelDistance - textWidth
Else
    labelX = centerX - taskWidth / 2 - labelDistance - textWidth
End If
labelY = centerY - textHeight / 2  ' 水平中心与任务条/里程碑中心水平对齐
textAlign = xlRight
```

### 4. 上方位置（"top"）

```vba
' 放在上方
If isMilestone Then
    ' 里程碑是菱形，标签居中于菱形上方
    ' 确保标签文本框中心与里程碑中心X坐标一致
    labelX = centerX - textWidth / 2  ' 水平居中于里程碑
    labelY = centerY - shape.Height / 2 - labelDistance - textHeight
    textAlign = xlCenter  ' 设置为居中对齐
Else
    ' 任务条是矩形，标签框左边与任务条左边垂直对齐
    labelX = taskLeft
    labelY = centerY - taskHeight / 2 - labelDistance - textHeight
    textAlign = xlLeft
End If
```

### 5. 下方位置（"bottom"）

```vba
' 放在下方
If isMilestone Then
    ' 里程碑是菱形，标签居中于菱形下方
    ' 确保标签文本框中心与里程碑中心X坐标一致
    labelX = centerX - textWidth / 2  ' 水平居中于里程碑
    labelY = centerY + shape.Height / 2 + labelDistance
    textAlign = xlCenter  ' 设置为居中对齐
Else
    ' 任务条是矩形，标签框左边与任务条左边垂直对齐
    labelX = taskLeft
    labelY = centerY + taskHeight / 2 + labelDistance
    textAlign = xlLeft
End If
```

### 6. 默认位置处理

如果未指定位置或指定了无效的位置，函数会使用默认的"right"位置：

```vba
' 里程碑（菱形）标签位置处理
' 里程碑不支持inside位置，如果未指定或无效，默认使用right
If textPosition = "" Or (textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom") Then
    textPosition = "right"
    modDebug.LogVerbose "里程碑未指定有效的Text Position，使用默认值: right", "modGantt.AddTaskLabelWithCoordinates2"
End If

' 任务条（矩形）标签位置处理
' 如果未指定或无效，默认使用right
If textPosition = "" Or (textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom" And textPosition <> "inside") Then
    textPosition = "right"
    modDebug.LogVerbose "任务条未指定有效的Text Position，使用默认值: right", "modGantt.AddTaskLabelWithCoordinates2"
End If
```

对于任务条的"inside"位置，如果标签放不下，会自动改为"right"位置：

```vba
' 如果指定为inside但放不下，改为right
If textPosition = "inside" And Not canFitInside Then
    textPosition = "right"
    modDebug.LogVerbose "任务条标签放不下inside位置，改为right", "modGantt.AddTaskLabelWithCoordinates2"
End If
```

## 组合形状和标签

标签创建后，会与任务条/里程碑形状（以及任务进度条，如果存在）组合在一起，便于整体管理。组合操作在`DrawTask`和`DrawMilestone`函数中进行，而不是在`AddTaskLabelWithCoordinates2`函数中，这样可以确保进度条也被包含在组合中。

### 任务条的组合处理

对于任务条，组合处理包括任务条、进度条（如果存在）和标签：

```vba
' 组合任务条、进度条和标签
Dim shapeGroup As Shape
On Error Resume Next

' 创建要组合的形状数组
Dim shapesToGroup As New Collection
shapesToGroup.Add taskShape.Name

' 如果有进度条，也加入组合
If Not progressShape Is Nothing Then
    shapesToGroup.Add progressShape.Name
    modDebug.LogVerbose "进度条将被包含在组合中，形状ID: " & progressShape.ID, "modGantt.DrawTask"
End If

shapesToGroup.Add labelShape.Name

' 将集合转换为数组
Dim shapesArray() As String
ReDim shapesArray(1 To shapesToGroup.Count)

Dim i As Long
For i = 1 To shapesToGroup.Count
    shapesArray(i) = shapesToGroup(i)
Next i

' 组合形状
Set shapeGroup = ws.Shapes.Range(shapesArray).Group
```

### 里程碑的组合处理

对于里程碑，组合处理只包括里程碑形状和标签：

```vba
' 组合里程碑和标签
Dim shapeGroup As Shape
On Error Resume Next

' 组合形状
Set shapeGroup = ws.Shapes.Range(Array(milestoneShape.Name, labelShape.Name)).Group
```

组合后的形状在计算行高和垂直居中时会作为一个整体处理：

```vba
' 计算任务和标签占用的总高度
Dim requiredHeight As Double
Dim topMost As Double, bottomMost As Double

' 检查形状是否已经组合
Dim parentGroup As Shape
On Error Resume Next
Set parentGroup = shapeObj.ParentGroup
On Error GoTo ErrorHandler

If Not parentGroup Is Nothing Then
    ' 形状已经在组中，直接使用组的尺寸
    modDebug.LogVerbose "形状已在组中，使用组的尺寸计算", "modGantt.AdjustRowHeightWithPadding"

    ' 获取组的上下边界
    topMost = parentGroup.Top
    bottomMost = parentGroup.Top + parentGroup.Height
    modDebug.LogVerbose "组边界 - 上: " & topMost & ", 下: " & bottomMost & ", 高度: " & parentGroup.Height, "modGantt.AdjustRowHeightWithPadding"
Else
    ' 形状未组合，分别计算形状和标签的边界
    ' ... 计算代码省略 ...
End If

' 计算所需高度（添加上下预留空隙）
requiredHeight = bottomMost - topMost + (padding * 2)
```

在调整垂直位置时，也会根据形状是否已组合采取不同的处理方式：

```vba
' 使用之前检查到的parentGroup
If Not parentGroup Is Nothing Then
    ' 形状已经在组中，移动整个组
    parentGroup.IncrementTop moveDistance
    modDebug.LogVerbose "移动形状组，ID: " & parentGroup.ID & ", 距离: " & moveDistance, "modGantt.AdjustRowHeightWithPadding"
Else
    ' 单独移动形状和标签
    shapeObj.IncrementTop moveDistance
    labelObj.IncrementTop moveDistance
    modDebug.LogVerbose "分别移动形状和标签，距离: " & moveDistance, "modGantt.AdjustRowHeightWithPadding"
End If
```

## 输出结果

函数返回创建的标签形状对象（Shape），如果创建失败则返回Nothing。

创建的标签具有以下特点：
- 文本内容来自任务的Description属性
- 字体大小为8，字体名称为Arial
- 水平对齐方式根据位置确定（左对齐、右对齐或居中）
- 垂直对齐方式为居中
- 文本框透明（无填充和边框）
- 文本框自动调整大小以适应文本内容
- 与任务条或里程碑形状组合在一起（如果组合成功）

## 函数调用逻辑

标签处理涉及多个函数之间的协作，形成一个完整的处理流程：

### 1. DrawTask 函数

`DrawTask` 函数负责绘制任务条，并调用标签处理函数为任务条添加标签。

#### 函数签名

```vba
Private Function DrawTask(task As Dictionary, ws As Worksheet, row As Long, timelineCoords As Dictionary) As Boolean
```

#### 实现细节

```vba
' 计算任务条位置和大小
Dim startX As Double, endX As Double, top As Double, width As Double, height As Double
' ... 计算代码省略 ...

' 创建任务条形状
Dim taskShape As Shape
Set taskShape = ws.Shapes.AddShape(msoShapeRectangle, startX, top, width, height)

' ... 设置任务条格式代码省略 ...

' 添加任务描述标签
Dim labelShape As Shape
Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT020", "5")) ' 从配置获取标签距离，默认5像素

Set labelShape = AddTaskLabelWithCoordinates2(task, taskShape, startX + width / 2, top + height / 2, CDbl(height), labelDistance)

' 组合任务条、进度条和标签，然后调整行高
If Not labelShape Is Nothing Then
    ' 组合形状
    Dim shapeGroup As Shape
    Dim shapesToGroup As New Collection
    shapesToGroup.Add taskShape.Name

    ' 如果有进度条，也加入组合
    If Not progressShape Is Nothing Then
        shapesToGroup.Add progressShape.Name
        modDebug.LogVerbose "进度条将被包含在组合中，形状ID: " & progressShape.ID, "modGantt.DrawTask"
    End If

    shapesToGroup.Add labelShape.Name

    ' 将集合转换为数组并组合形状
    Dim shapesArray() As String
    ReDim shapesArray(1 To shapesToGroup.Count)

    Dim i As Long
    For i = 1 To shapesToGroup.Count
        shapesArray(i) = shapesToGroup(i)
    Next i

    Set shapeGroup = ws.Shapes.Range(shapesArray).Group

    ' 动态调整行高
    Dim rowPadding As Long
    rowPadding = Val(GetConfigValue("GT026", "3")) ' 从配置获取行间距，默认3像素
    AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
End If
```

### 2. DrawMilestone 函数

`DrawMilestone` 函数负责绘制里程碑，并调用标签处理函数为里程碑添加标签。

#### 函数签名

```vba
Private Function DrawMilestone(task As Dictionary, ws As Worksheet, row As Long, timelineCoords As Dictionary) As Boolean
```

#### 实现细节

```vba
' 计算里程碑位置和大小
Dim milestoneX As Double, milestoneY As Double, milestoneSize As Long
' ... 计算代码省略 ...

' 创建里程碑形状（菱形）
Dim milestoneShape As Shape
Set milestoneShape = ws.Shapes.AddShape(msoShapeDiamond, milestoneX - milestoneSize / 2, milestoneY - milestoneSize / 2, milestoneSize, milestoneSize)

' ... 设置里程碑格式代码省略 ...

' 添加里程碑描述标签
Dim labelShape As Shape
Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT020", "5")) ' 从配置获取标签距离，默认5像素

Set labelShape = AddTaskLabelWithCoordinates2(task, milestoneShape, milestoneX, milestoneY, CDbl(milestoneSize), labelDistance)

' 组合里程碑和标签，然后调整行高
If Not labelShape Is Nothing Then
    ' 组合形状
    Dim shapeGroup As Shape
    On Error Resume Next

    ' 组合形状
    Set shapeGroup = ws.Shapes.Range(Array(milestoneShape.Name, labelShape.Name)).Group

    If Err.Number <> 0 Then
        modDebug.LogWarning "无法组合形状: " & Err.Number & " - " & Err.Description, "modGantt.DrawMilestone"
        Err.Clear
    Else
        modDebug.LogVerbose "成功组合形状，组ID: " & shapeGroup.ID, "modGantt.DrawMilestone"
    End If

    On Error GoTo ErrorHandler

    ' 动态调整行高
    Dim rowPadding As Long
    rowPadding = Val(GetConfigValue("GT026", "3")) ' 从配置获取行间距，默认3像素
    AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
End If
```

### 3. 完整调用链

整个标签处理的调用链如下：

1. `DrawTasksAndMilestones` 函数遍历所有任务和里程碑
2. 对于任务类型（Type="A"），调用 `DrawTask` 函数
3. 对于里程碑类型（Type="M"），调用 `DrawMilestone` 函数
4. `DrawTask` 和 `DrawMilestone` 函数创建形状后，调用 `AddTaskLabelWithCoordinates2` 函数添加标签
5. 标签创建成功后，调用 `AdjustRowHeightWithPadding` 函数调整行高并确保形状和标签垂直居中

这种层次化的调用结构使得代码模块化，每个函数专注于自己的职责，便于维护和扩展。

## 流程图

```mermaid
flowchart TD
    A[开始] --> B[获取任务描述]
    B --> C{描述为空?}
    C -->|是| D[返回Nothing]
    C -->|否| E[判断形状类型]
    E --> F{是里程碑?}

    F -->|是| G[计算文本尺寸]
    F -->|否| H[计算文本尺寸]

    G --> I{TextPosition=?}
    I -->|right| J1[计算右侧位置]
    I -->|left| J2[计算左侧位置]
    I -->|top| J3[计算上方位置]
    I -->|bottom| J4[计算下方位置]
    I -->|其他| J5[默认使用right位置]

    H --> K{TextPosition=?}
    K -->|right| L1[计算右侧位置]
    K -->|left| L2[计算左侧位置]
    K -->|top| L3[计算上方位置]
    K -->|bottom| L4[计算下方位置]
    K -->|inside| L5{标签能放在内部?}
    K -->|其他| L6[默认使用right位置]

    L5 -->|是| L7[计算内部位置]
    L5 -->|否| L1

    J1 --> M[创建标签文本框]
    J2 --> M
    J3 --> M
    J4 --> M
    J5 --> M
    L1 --> M
    L2 --> M
    L3 --> M
    L4 --> M
    L6 --> M
    L7 --> M

    M --> N{创建成功?}
    N -->|否| D
    N -->|是| O[设置标签文本和格式]
    O --> P[设置标签透明]
    P --> Q[返回标签形状]
    Q --> R{是任务条?}
    R -->|是| S1{有进度条?}
    R -->|否| S2[组合里程碑和标签]
    S1 -->|是| T1[组合任务条、进度条和标签]
    S1 -->|否| T2[组合任务条和标签]
    T1 --> U[调整行高]
    T2 --> U
    S2 --> U
    U --> V[结束]
    D --> V
```

## 注意事项
10. [形状Placement属性设置](#形状Placement属性设置)

1. **里程碑与任务条的区别**：
   - 里程碑（菱形）和任务条（矩形）的标签位置计算逻辑不同
   - 里程碑标签在上/下位置时会水平居中于菱形
   - 任务条标签在上/下位置时会左对齐于任务条左边缘
   - 里程碑标签位置计算使用菱形的实际宽度和高度属性（shape.Width和shape.Height）

2. **TextPosition属性的有效值**：
   - 任务条：left、right、top、bottom、inside
   - 里程碑：left、right、top、bottom（不支持inside）

3. **默认标签位置**：
   - 如果未指定TextPosition或指定了无效的值，函数会使用默认的"right"位置
   - 对于任务条的"inside"位置，如果标签放不下，会自动改为"right"位置

4. **组合形状和标签**：
   - 将标签和任务条/里程碑组合在一起，便于整体管理
   - 对于任务条，如果有进度条，也将其包含在组合中
   - 组合操作在DrawTask和DrawMilestone函数中进行，而不是在AddTaskLabelWithCoordinates2函数中
   - 计算组合的整体高度，用于调整行高
   - 确保组合垂直居中于行，提高视觉美观性

5. **错误处理**：
   - 如果描述为空，函数会返回Nothing
   - 如果创建标签失败，函数会记录错误并返回Nothing
   - 调用方会检查返回值，如果为Nothing则不会尝试调整行高

6. **性能考虑**：
   - 创建临时文本框计算文本尺寸是一个相对耗时的操作
   - 函数使用详细的日志记录，有助于调试但可能影响性能

## 形状Placement属性设置

为了避免在调整行高时形状（Shape）被拉伸，系统对所有形状设置了Placement属性为xlMove。

### 1. Placement属性的作用

在Excel VBA中，Shape对象的Placement属性控制形状与其底层单元格的行为关系。它有以下三个可能的值：

- **xlMoveAndResize (1)** - 默认值：形状会随着单元格的移动和大小调整而移动和调整大小。这会导致在调整行高时形状被拉伸。
- **xlMove (2)** - 我们使用的值：形状会随着单元格的移动而移动，但其大小保持不变。调整行高或列宽只会移动形状的位置，而不会改变其形状比例。
- **xlFreeFloating (3)** - 形状与单元格完全独立，不会随着单元格的移动或大小调整而改变位置或大小。

### 2. 应用Placement属性的位置

系统在以下位置设置了Placement属性为xlMove：

1. **里程碑形状**：
   ```vba
   ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
   milestoneShape.Placement = xlMove
   ```

2. **标签形状**：
   ```vba
   ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
   labelShape.Placement = xlMove
   ```

3. **任务条形状**：
   ```vba
   ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
   taskShape.Placement = xlMove
   ```

4. **进度条形状**：
   ```vba
   ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
   progressShape.Placement = xlMove
   ```

5. **形状组**：
   ```vba
   ' 设置组的Placement属性为xlMove，避免在调整行高时被拉伸
   shapeGroup.Placement = xlMove
   ```

### 3. 设置Placement属性的时机

Placement属性在形状创建后立即设置，确保在后续的行高调整过程中形状不会被拉伸：

1. 创建形状（里程碑、任务条、标签、进度条）
2. 设置Placement属性为xlMove
3. 设置形状的其他属性（颜色、边框等）
4. 组合形状（如果需要）
5. 为组合后的形状设置Placement属性为xlMove
6. 调整行高并垂直居中

### 4. 解决的问题

设置Placement属性为xlMove解决了以下问题：

- 防止在调整行高时里程碑（菱形）被垂直拉伸，保持其正确的形状比例
- 防止标签文本框在调整行高时被拉伸，保持文本的正确显示
- 防止任务条和进度条在调整行高时被垂直拉伸，保持其预期的高度
- 确保形状组在调整行高时保持其内部元素的相对位置和大小

通过这种方式，甘特图中的所有形状都能在行高调整后保持其原始比例和视觉效果，提高了图表的专业性和美观性。

### 5. Excel行高舍入局限性

在使用`AdjustRowHeightWithPadding`函数调整行高时，我们发现Excel VBA存在一个局限性：当设置精确的行高值时，Excel可能会进行舍入或截断。例如：

```vba
' 尝试设置行高为33.3043308258057
ws.Rows(row).RowHeight = 33.3043308258057

' 但实际设置后的行高变成了33
Dim actualHeight As Double
actualHeight = ws.Rows(row).Height  ' 此时actualHeight = 33
```

这种舍入行为会导致以下影响：

1. **Y坐标计算差异**：
   - 当在同一行绘制多个形状时，如果行高在绘制过程中被调整，后续形状的Y坐标计算会使用舍入后的行高
   - 这会导致同一行的形状在Y方向上有微小的偏差，而不是完全重合

2. **垂直居中调整**：
   - 系统会尝试通过`AdjustRowHeightWithPadding`函数中的垂直居中调整来补偿这种差异
   - 但由于舍入误差，补偿可能不完全精确

3. **实际影响**：
   - 在大多数实际使用场景中，这种微小的偏差不会明显影响甘特图的视觉效果
   - 只有在同一行绘制多个完全重叠的形状时（如相同日期的多个里程碑）才会注意到这种差异

**注意**：由于实际项目中很少需要在同一行绘制完全重合的形状，我们选择接受这个Excel的局限性，而不是增加复杂的补偿逻辑。如果将来需要解决这个问题，可以考虑在第一次计算行中心点后缓存该值，供同一行的所有形状使用。
