# 配置工作表设计

配置工作表用于存储系统配置参数，为系统提供可配置的选项，便于后续扩展开发。第一版本需要考虑甘特图中项目名称/项目经理信息，以及时间表头部分的字体、颜色、字号以及甘特图时间最小单元的宽度放大系数。

**工作表名称**: `Config`

## 配置表结构

| 字段名 | 数据类型 | 说明 | 默认值 | 所属模块 |
| ------ | -------- | ---- | ------ | -------- |
| ConfigID | 文本 | 配置项的唯一标识符 | - | - |
| ConfigName | 文本 | 配置项的名称 | - | - |
| ConfigValue | 文本/数字 | 配置项的值 | - | - |
| Description | 文本 | 配置项的描述 | - | - |
| Module | 文本 | 所属模块（UI/Gantt/Data/Main） | - | - |
| Category | 文本 | 配置类别 | - | - |
| IsEnabled | 布尔值 | 是否启用该配置项。当设置为False时，GetModuleConfig函数会忽略该配置项，返回默认值。这允许临时禁用某些配置项而不需要删除它们。 | True | - |

## 配置项列表

### UI模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| UI001 | ProjectNameFont | Barlow | 项目名称字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI002 | ProjectNameFontSize | 18 | 项目名称字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI003 | ProjectNameFontBold | True | 项目名称是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI004 | ProjectNameFontColor | #D3E7E3 | 项目名称字体颜色(十六进制RGB颜色代码，如#D3E7E3=浅绿色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI005 | ProjectNameBackColor | #EB5A50 | 项目名称背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI006 | ProjectNameBorderStyle | none | 项目名称边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI007 | ProjectNameRowHeight | auto | 项目名称行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI008 | ProjectManagerFont | Barlow | 项目经理名称字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI009 | ProjectManagerFontSize | 11 | 项目经理名称字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI010 | ProjectManagerFontBold | False | 项目经理名称是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI011 | ProjectManagerFontColor | #D3E7E3 | 项目经理名称字体颜色(十六进制RGB颜色代码，如#D3E7E3=浅绿色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI012 | ProjectManagerBackColor | #EB5A50 | 项目经理名称背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI013 | ProjectManagerBorderStyle | none | 项目经理名称边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI014 | ProjectManagerRowHeight | auto | 项目经理名称行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI015 | SupplementInfoFont | Barlow | 补充信息字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI016 | SupplementInfoFontSize | 9 | 补充信息字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI017 | SupplementInfoFontBold | False | 补充信息是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI018 | SupplementInfoFontColor | #FFFFFF | 补充信息字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI019 | SupplementInfoBackColor | #EB5A50 | 补充信息背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI020 | SupplementInfoBorderStyle | none | 补充信息边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI021 | SupplementInfoRowHeight | auto | 补充信息行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI022 | TimelineYearFont | Arial | 时间轴年字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI023 | TimelineYearFontSize | 11 | 时间轴年字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI024 | TimelineYearFontBold | True | 时间轴年是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI025 | TimelineYearFontColor | #FFFFFF | 时间轴年字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI026 | TimelineYearBackColor | #EB5A50 | 时间轴年背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI027 | TimelineYearBorderStyle | all | 时间轴年边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI028 | TimelineYearRowHeight | auto | 时间轴年行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI029 | TimelineMonthFont | Arial | 时间轴月字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI030 | TimelineMonthFontSize | 10 | 时间轴月字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI031 | TimelineMonthFontBold | False | 时间轴月是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI032 | TimelineMonthFontColor | #FFFFFF | 时间轴月字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI033 | TimelineMonthBackColor | #EB5A50 | 时间轴月背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI034 | TimelineMonthBorderStyle | all | 时间轴月边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI035 | TimelineMonthRowHeight | auto | 时间轴月行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI036 | TimelineWeekFont | Arial | 时间轴周字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI037 | TimelineWeekFontSize | 9 | 时间轴周字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI038 | TimelineWeekFontBold | False | 时间轴周是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI039 | TimelineWeekFontColor | #FFFFFF | 时间轴周字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI040 | TimelineWeekBackColor | #EB5A50 | 时间轴周背景颜色(十六进制RGB颜色代码，如#EB5A50=红色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI041 | TimelineWeekBorderStyle | all | 时间轴周边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI042 | TimelineWeekRowHeight | auto | 时间轴周行高(auto=自动调整，或指定数值) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI043 | CategoryFont | Barlow | 任务类别字体(可使用任何系统安装的字体名称) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI044 | CategoryFontSize | 11 | 任务类别字号(正整数，单位为磅) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI045 | CategoryFontBold | True | 任务类别是否粗体(True=粗体，False=常规) | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI046 | CategoryFontColor | #000000 | 任务类别字体颜色(十六进制RGB颜色代码，如#000000=黑色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI047 | CategoryBackColor | #F2F2F2 | 任务类别背景颜色(十六进制RGB颜色代码，如#F2F2F2=浅灰色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI048 | CategoryBorderStyle | outline | 任务类别边框样式(none=无/all=全部/outline=轮廓/bottom=底部) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI049 | CategoryColumnWidth | 30 | 任务类别列宽(正整数，单位为标准列宽) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI050 | CategoryWrapText | True | 任务类别是否自动换行(True=自动换行，False=不自动换行) | UI | Style | 在modUI.ApplyGanttTheme中使用 |
| UI051 | ColumnAWidth | 2 | A列宽度(正整数，单位为标准列宽) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI052 | ChartTheme | custom | 甘特图主题(1-5=预设主题/custom=自定义主题) | UI | Theme | 在modUI.ApplyGanttTheme中使用 |
| UI058 | EnableLogo | True | 是否启用Logo(True=启用，False=禁用) | UI | Feature | 在modUI.ApplyGanttTheme中使用 |
| UI059 | LogoMargin | 2 | Logo边距(正整数，单位为像素) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI053 | ChartGridlinesArea | all | 网格线应用区域(all=全部区域/header=表头/ganttDrawing=甘特图绘图区/taskcategory=任务类别区/header,ganttDrawing=表头和甘特图绘图区/header,taskcategory=表头和任务类别区/ganttDrawing,taskcategory=甘特图绘图区和任务类别区) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI054 | ChartGridlinesType | all | 网格线类型(all=所有网格线/horizontal=水平线/vertical=垂直线) | UI | Layout | 在modUI.ApplyGanttTheme中使用 |
| UI055 | ChartGridlineColor | #DDDDDD | 网格线颜色(十六进制RGB颜色代码，如#DDDDDD=浅灰色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI056 | ChartBackgroundColor | #FFFFFF | 甘特图背景颜色(十六进制RGB颜色代码，如#FFFFFF=白色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI057 | ChartAlternateRowColor | #F5F5F5 | 甘特图交替行颜色(十六进制RGB颜色代码，如#F5F5F5=浅灰色) | UI | Color | 在modUI.ApplyGanttTheme中使用 |

### Gantt模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| GT001 | CellWidthFactor | 0.8 | 甘特图时间单元格宽度放大系数(大于0的小数，1.0表示标准宽度) | Gantt | Layout | 在modGantt.CreateTimeline中通过GetConfig("CellWidthFactor", 0.8)获取 |
| GT002 | DefaultTaskColor | #3366CC | 默认任务条颜色(十六进制RGB颜色代码，如#3366CC=蓝色) | Gantt | Color | 在modData.GetAllTasks中通过GetConfig("DefaultTaskColor", "#3366CC")获取 |
| GT003 | DefaultMilestoneColor | #FF9900 | 默认里程碑颜色(十六进制RGB颜色代码，如#FF9900=橙色) | Gantt | Color | 在modData.GetAllTasks中通过GetConfig("DefaultMilestoneColor", "#FF9900")获取 |
| GT004 | TaskProgressColor | #66CC66 | 任务进度颜色(十六进制RGB颜色代码，如#66CC66=绿色) | Gantt | Color | 在modGantt.DrawTask中通过GetConfig("TaskProgressColor", "#66CC66")获取 |
| GT005 | CurrentDateLineColor | #41B7AC | 当前日期线颜色(十六进制RGB颜色代码，如#41B7AC=青色) | Gantt | Color | 在modGantt.DrawCurrentDateLine中通过GetConfig("CurrentDateLineColor", "#41B7AC")获取 |
| GT006 | CurrentDateLineStyle | 2 | 当前日期线样式(1=实线，2=虚线) | Gantt | Style | 在modGantt.DrawCurrentDateLine中通过GetConfig("CurrentDateLineStyle", 2)获取 |
| GT018 | EnableCurrentDateLine | False | 是否启用当前日期线(True=启用，False=禁用) | Gantt | Feature | 在modGantt.DrawTasksAndMilestones中通过GetConfig("EnableCurrentDateLine", False)获取 |
| GT019 | CurrentDateLineWeight | 0.8 | 当前日期线宽度(大于0的小数，单位为磅) | Gantt | Style | 在modGantt.DrawCurrentDateLine中通过GetConfig("CurrentDateLineWeight", 0.8)获取 |
| GT007 | DefaultTaskPosition | next | 默认任务位置(next=下一行/same=同一行/数字=指定行号) | Gantt | Layout | 在modData.GetAllTasks中通过GetConfig("DefaultTaskPosition", "next")获取 |
| GT008 | DefaultTaskTextPosition | right | 默认任务文字位置(left=左侧/right=右侧/top=顶部/bottom=底部/inside=内部) | Gantt | Layout | 在modData.GetAllTasks中通过GetConfig("DefaultTaskTextPosition", "right")获取 |
| GT009 | DefaultMilestoneTextPosition | right | 默认里程碑文字位置(left=左侧/right=右侧/top=顶部/bottom=底部) | Gantt | Layout | 在modData.GetAllTasks中通过GetConfig("DefaultMilestoneTextPosition", "right")获取 |
| GT010 | BaselineColor | #FF0000 | 基准线颜色(十六进制RGB颜色代码，如#FF0000=红色) | Gantt | Color | 在modGantt.DrawBaselineWithBounds中通过GetConfig("BaselineColor", "#FF0000")获取 |
| GT011 | BaselineStyle | 2 | 基准线样式(1=实线，2=虚线) | Gantt | Style | 在modGantt.DrawBaselineWithBounds中通过GetConfig("BaselineStyle", 2)获取 |
| GT012 | BaselineWeight | 0.8 | 基准线宽度(大于0的小数，单位为磅) | Gantt | Style | 在modGantt.DrawBaselineWithBounds中通过GetConfig("BaselineWeight", 0.8)获取 |
| GT013 | TaskBarHeight | 11 | 任务条高度/里程碑大小(正整数，单位为像素) | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfig("TaskBarHeight", 11)获取 |
| GT014 | TaskBarBorderWidth | 0 | 任务条/里程碑边框宽度(0=无边框，正整数表示边框宽度，单位为磅) | Gantt | Style | 在modGantt.DrawTask和DrawMilestone中通过GetConfig("TaskBarBorderWidth", 0)获取 |
| GT015 | ProgressBarColor | #66CC66 | 进度条颜色(十六进制RGB颜色代码，如#66CC66=绿色) | Gantt | Color | 在modGantt.DrawTask中通过GetConfig("ProgressBarColor", "#66CC66")获取 |
| GT016 | LabelDistance | 5 | 标签与任务条/里程碑的距离(正整数，单位为像素) | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfig("LabelDistance", 5)获取 |
| GT017 | RowPadding | 3 | 行高上下预留空隙(正整数，单位为像素) | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfig("RowPadding", 3)获取 |
| GT020 | MilestoneShapeStyle | 1 | 里程碑形状样式(1=菱形，更多选项见config_table.txt) | Gantt | Style | 在modGantt.DrawMilestone中通过GetConfig("MilestoneShapeStyle", 1)获取 |
| GT021 | TaskBarShapeStyle | 1 | 任务条形状样式(1=矩形，更多选项见config_table.txt) | Gantt | Style | 在modGantt.DrawTask中通过GetConfig("TaskBarShapeStyle", 1)获取 |
| GT022 | EnableSpotlight | True | 是否启用聚光灯效果(True=启用，False=禁用) | Gantt | Feature | 在modGanttSpotlight.InitializeGanttSpotlight中通过GetConfig("EnableSpotlight", True)获取 |
| GT023 | SpotlightMode | all | 聚光灯模式(all=水平和垂直/horizontal=仅水平/vertical=仅垂直) | Gantt | Feature | 在modGanttSpotlight.InitializeGanttSpotlight中通过GetConfig("SpotlightMode", "all")获取 |
| GT024 | SpotlightColor | #E6F2FF | 聚光灯颜色(十六进制RGB颜色代码，如#E6F2FF=浅蓝色) | Gantt | Color | 在modGanttSpotlight.InitializeGanttSpotlight中通过GetConfig("SpotlightColor", "#E6F2FF")获取 |
| GT025 | EnableGanttBorder | True | 是否启用甘特图外边框(True=启用，False=禁用) | Gantt | Feature | 在modGantt.ApplyGanttBorder中通过GetConfig("EnableGanttBorder", True)获取 |
| GT026 | GanttBorderColor | #D3D3D3 | 甘特图外边框颜色(十六进制RGB颜色代码，如#D3D3D3=浅灰色) | Gantt | Color | 在modGantt.ApplyGanttBorder中通过GetConfig("GanttBorderColor", "#D3D3D3")获取 |
| GT027 | GanttBorderWeight | 1 | 甘特图外边框宽度(正整数，单位为磅) | Gantt | Style | 在modGantt.ApplyGanttBorder中通过GetConfig("GanttBorderWeight", 1)获取 |
| GT028 | GanttBorderStyle | 1 | 甘特图外边框样式(1=实线，2=虚线) | Gantt | Style | 在modGantt.ApplyGanttBorder中通过GetConfig("GanttBorderStyle", 1)获取 |

### Data模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| DT001 | AutoCalculateDuration | True | 是否自动计算任务持续时间(True=自动计算，False=使用输入值) | Data | Calculation | 在modData.GetAllTasks中通过GetConfig("AutoCalculateDuration", True)获取 |
| DT002 | ExcludeWeekends | True | 计算持续时间时是否排除周末(True=排除周末，False=包含周末) | Data | Calculation | 在modData.GetAllTasks中通过GetConfig("ExcludeWeekends", True)获取 |
| DT003 | DefaultTaskProgress | 0 | 默认任务进度(0-100的整数，表示完成百分比) | Data | Default | 在modData.GetAllTasks中通过GetConfig("DefaultTaskProgress", 0)获取 |

### Debug模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| DB001 | EnableDebug | False | 是否开启debug模式(True=开启，False=关闭) | Debug | Default | 在modDebug.InitDebug中通过GetConfig("EnableDebug", False)获取 |
| DB002 | DebugLevel | 4 | 调试级别(1=错误,2=警告,3=信息,4=详细) | Debug | Default | 在modDebug.InitDebug中通过GetConfig("DebugLevel", 4)获取 |
| DB003 | EnableFileLogging | False | 是否启用文件日志(True=启用，False=禁用) | Debug | Default | 在modDebug.InitDebug中通过GetConfig("EnableFileLogging", False)获取 |
| DB004 | EnableImmediateOutput | False | 是否输出到即时窗口(True=输出，False=不输出) | Debug | Default | 在modDebug.InitDebug中通过GetConfig("EnableImmediateOutput", False)获取 |
| DB005 | UTF8Encoding | True | 是否使用UTF-8编码日志文件(True=使用UTF-8，False=使用默认编码) | Debug | Default | 在modDebug.InitDebug中通过GetConfig("UTF8Encoding", True)获取 |

## 配置访问函数

系统中使用以下函数访问配置项：

```vba
' 直接获取配置项的值，不考虑模块，支持默认值
Public Function GetConfig(configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    Dim tbl As ListObject
    Dim dataArray As Variant
    Dim i As Long, enabledCol As Long, nameCol As Long, valueCol As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值
    If tbl.DataBodyRange Is Nothing Then
        GetConfig = defaultValue
        modDebug.LogInfo "配置表为空，使用默认值: " & defaultValue, "modData.GetConfig"
        modDebug.LogFunctionExit "modData.GetConfig"
        Exit Function
    End If

    ' 获取列索引（相对于ListObject）
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    dataArray = tbl.DataBodyRange.Value

    ' 计算数组中的相对列索引
    ' 在Excel Range.Value数组中，第一列的索引是1，不管ListObject的第一列是什么
    Dim enabledColArray As Long, nameColArray As Long, valueColArray As Long
    Dim firstColIndex As Long

    ' 获取ListObject第一列的索引
    firstColIndex = tbl.ListColumns(1).Index

    ' 计算数组中的相对列索引
    enabledColArray = enabledCol - firstColIndex + 1
    nameColArray = nameCol - firstColIndex + 1
    valueColArray = valueCol - firstColIndex + 1

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果配置名称匹配且配置已启用
        If dataArray(i, nameColArray) = configName And dataArray(i, enabledColArray) = True Then
            GetConfig = dataArray(i, valueColArray)
            modDebug.LogVerbose "找到配置项 " & configName & "，值为: " & GetConfig, "modData.GetConfig"
            modDebug.LogFunctionExit "modData.GetConfig"
            Exit Function
        End If
    Next i

    ' 如果没有找到配置项，返回默认值
    GetConfig = defaultValue
    modDebug.LogVerbose "配置项 " & configName & " 不存在或未启用，使用默认值: " & defaultValue, "modData.GetConfig"
    modDebug.LogFunctionExit "modData.GetConfig"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfig"
    GetConfig = defaultValue
    modDebug.LogFunctionExit "modData.GetConfig", "错误: " & Err.Description
End Function

' 从配置字典中获取值，支持默认值
Public Function GetConfigFromDict(dict As Dictionary, configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    If dict.Exists(configName) Then
        GetConfigFromDict = dict(configName)
    Else
        GetConfigFromDict = defaultValue
        modDebug.LogVerbose "配置项 " & configName & " 在字典中不存在，使用默认值: " & defaultValue, "modData.GetConfigFromDict"
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfigFromDict"
    GetConfigFromDict = defaultValue
End Function
```

## 配置项访问模式

在实际代码中，配置项的访问主要采用以下模式：

### 1. 直接使用GetConfig获取配置项值

这是推荐的方式，直接通过配置名称获取配置值，不考虑模块：

```vba
' 直接获取配置项值，使用默认值
Dim fontName As String
fontName = GetConfig("ProjectNameFont", "Barlow")

' 使用获取的配置设置项目名称格式
With ws.Cells(1, 2)
    .Value = projectInfo("ProjectName")
    .Font.Name = fontName
    .Font.Size = GetConfig("ProjectNameFontSize", 14)
    .Font.Bold = GetConfig("ProjectNameFontBold", True)
    .Font.Color = modUtilities.GetRGBColor(GetConfig("ProjectNameFontColor", "#000000"))
End With
```

### 2. 使用GetConfigFromDict从配置字典中获取单个值

当从配置字典中获取单个值时，可以使用GetConfigFromDict函数，它支持提供默认值：

```vba
' 从配置字典中获取值
Dim defaults As Dictionary
Set defaults = modConfigDefaults.GetDefaults()
Dim fontName As String
fontName = GetConfigFromDict(defaults, "ProjectNameFont", "Barlow")
```

### 3. 性能优化建议

在循环中访问配置项时，应该在循环外获取配置，避免重复查询：

```vba
' 在循环外获取配置
Dim taskBarHeight As Long
Dim taskBarBorderWidth As Single
Dim progressBarColor As String
Dim labelDistance As Long
Dim rowPadding As Long

' 获取任务条高度
taskBarHeight = CLng(Val(GetConfig("TaskBarHeight", 11)))

' 获取任务条边框宽度
taskBarBorderWidth = CSng(Val(GetConfig("TaskBarBorderWidth", 0)))

' 获取进度条颜色
progressBarColor = CStr(GetConfig("ProgressBarColor", "#66CC66"))

' 获取标签距离
labelDistance = CLng(Val(GetConfig("LabelDistance", 5)))

' 获取行高预留空隙
rowPadding = CLng(Val(GetConfig("RowPadding", 3)))

' 在循环中直接使用
For i = 1 To taskCount
    ' 使用taskBarHeight, taskBarBorderWidth等，避免重复查询
    ' ...
Next i
```

### 4. 默认值管理

所有默认值都集中在modConfigDefaults模块中的GetDefaults函数中管理：

```vba
' 获取所有默认配置
Dim defaults As Dictionary
Set defaults = modConfigDefaults.GetDefaults()

' 使用默认配置
Dim fontName As String
fontName = defaults("ProjectNameFont") ' 返回 "Barlow"
```

### 5. 特殊配置项说明

#### 5.1 当前日期线配置

当前日期线是甘特图中显示当前日期的垂直线，可以帮助用户直观地看到项目进度与当前日期的关系。相关配置项包括：

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| EnableCurrentDateLine | False | 是否启用当前日期线功能，默认为禁用 |
| CurrentDateLineColor | #41B7AC | 当前日期线的颜色，默认为青色 |
| CurrentDateLineStyle | 2 | 当前日期线的样式，默认为虚线(2) |
| CurrentDateLineWeight | 0.8 | 当前日期线的宽度，默认为0.8磅 |

当前日期线的绘制逻辑：
1. 在`modGantt.DrawTasksAndMilestones`函数中，在绘制完所有基准线后，检查`EnableCurrentDateLine`配置是否为True
2. 如果启用了当前日期线功能，获取当前系统日期
3. 检查当前日期是否在甘特图时间轴范围内
4. 如果在范围内，调用`DrawCurrentDateLine`函数绘制当前日期线
5. `DrawCurrentDateLine`函数使用与`DrawBaselineWithBounds`类似的逻辑，但使用当前日期线的专用配置参数

#### 5.2 基准线配置

基准线用于在甘特图中显示项目的关键日期点，如计划开始日期、里程碑日期等。相关配置项包括：

| ConfigName | 默认值 | 说明 |
| ---------- | ------ | ---- |
| BaselineColor | #FF0000 | 基准线的颜色，默认为红色 |
| BaselineStyle | 2 | 基准线的样式，默认为虚线(2) |
| BaselineWeight | 0.8 | 基准线的宽度，默认为0.8磅 |

基准线的数据来源于任务数据中的"Baseline"字段，系统会自动去重，确保每个日期只绘制一条基准线。

### 6. 配置项访问的关键逻辑

1. **错误处理**：所有配置项访问都包含错误处理，确保即使配置表不存在或配置项不存在，代码也能继续运行。
2. **默认值**：GetConfig函数支持提供默认值，确保即使配置项不存在或被禁用，也能使用合理的默认值。
3. **类型转换**：从配置表获取的值都是Variant类型，需要根据实际使用进行类型转换（如CStr、CDbl、CBool等）。
4. **唯一标识**：每个配置项都有唯一的ConfigName，不再按模块分组，简化了配置项的管理和访问。
5. **启用/禁用**：配置表中的IsEnabled字段允许临时禁用某些配置项，而不需要删除它们。GetConfig函数会检查IsEnabled字段，只返回启用的配置项。
6. **ListObject访问**：代码使用ListObject对象模型访问配置表，而不是直接访问单元格，这使得代码更加健壮，不受表格位置变化的影响。
7. **性能优化**：
   - 使用数组处理代替单元格访问：GetConfig函数将整个数据区域一次性读取到数组中，然后在内存中处理，避免了多次访问Excel对象模型，显著提高了性能。
   - 预先获取列索引：在循环外提前获取所需列的索引，避免在循环中重复查找。
