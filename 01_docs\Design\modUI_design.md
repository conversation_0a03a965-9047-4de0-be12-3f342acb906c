# Project Management Gantt Chart System - UI模块设计

本文档描述了UI模块(modUI)的设计，包括其功能、结构和实现细节。

## 1. 模块概述

UI模块负责用户界面交互和显示，处理工作表的准备、格式化和用户交互。它是系统中负责视觉呈现的关键模块，确保甘特图工作表的正确创建和格式化。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 主要功能

UI模块提供以下主要功能：

1. **准备甘特图工作表**：创建和格式化用于显示甘特图的工作表
2. **生成时间轴**：根据项目日期范围创建时间轴，并确定坐标原点
3. **设置表头**：设置甘特图工作表的表头，显示项目信息
4. **应用格式**：应用适当的格式和样式到工作表元素
5. **设置网格线**：设置工作表网格线，提高可读性

## 3. 模块结构

### 3.1 公共函数

| 函数名 | 描述 |
|--------|------|
| PrepareGanttWorksheet | 准备甘特图工作表的主函数，协调其他函数的执行 |

### 3.2 私有函数

| 函数名 | 描述 |
|--------|------|
| DeleteGanttWorksheetIfExists | 检查并删除已存在的甘特图工作表 |
| CreateGanttWorksheet | 创建新的甘特图工作表 |
| FormatGanttWorksheet | 设置工作表的基本格式 |
| SetupHeaders | 设置表头，显示项目信息 |
| GenerateTimeline | 生成时间轴并确定坐标原点 |
| SetupGridlines | 设置工作表网格线 |

## 4. 函数详细说明

### 4.1 PrepareGanttWorksheet

```vba
Public Sub PrepareGanttWorksheet()
```

**功能**：准备甘特图工作表的主函数，协调其他函数的执行。

**执行流程**：
1. 检查并删除旧的甘特图工作表
2. 创建新的甘特图工作表
3. 设置工作表格式
4. 设置表头
5. 生成时间轴
6. 设置网格线

**错误处理**：捕获并记录错误，然后重新抛出错误。

### 4.2 DeleteGanttWorksheetIfExists

```vba
Private Sub DeleteGanttWorksheetIfExists()
```

**功能**：检查并删除已存在的甘特图工作表。

**执行流程**：
1. 尝试获取名为"GanttChart"的工作表
2. 如果工作表存在，则删除它
3. 使用On Error Resume Next防止工作表不存在时出错

### 4.3 CreateGanttWorksheet

```vba
Private Function CreateGanttWorksheet() As Worksheet
```

**功能**：创建新的甘特图工作表。

**执行流程**：
1. 添加新工作表
2. 设置工作表名称为"GanttChart"
3. 设置工作表标签颜色
4. 返回新创建的工作表对象

**返回值**：新创建的工作表对象。

### 4.4 FormatGanttWorksheet

```vba
Private Sub FormatGanttWorksheet(ws As Worksheet)
```

**功能**：设置工作表的基本格式。

**参数**：
- ws：要格式化的工作表对象

**执行流程**：
1. 获取UI和Gantt模块配置
2. 设置列宽
3. 设置行高
4. 设置字体
5. 设置背景色

### 4.5 SetupHeaders

```vba
Private Sub SetupHeaders(ws As Worksheet)
```

**功能**：设置表头，显示项目信息。

**参数**：
- ws：要设置表头的工作表对象

**执行流程**：
1. 设置最近更新信息（合并B3:B5单元格）
2. 显示最近更新日期

**注意**：项目名称和项目经理信息的设置已移至modGantt.MergeProjectInfoCells函数中

### 4.6 GenerateTimeline

```vba
Private Function GenerateTimeline(ws As Worksheet) As Dictionary
```

**功能**：生成时间轴并确定坐标原点。

**参数**：
- ws：要生成时间轴的工作表对象

**执行流程**：
1. 获取项目信息和甘特图配置
2. 确定时间单位（日/周/月）
3. 设置时间轴起始位置
4. 根据时间单位生成不同的时间轴
5. 设置时间轴区域的格式
6. 返回时间轴信息字典

**返回值**：包含时间轴信息的字典，包括坐标原点、时间单位、开始日期和结束列。

### 4.7 SetupGridlines

```vba
Private Sub SetupGridlines(ws As Worksheet)
```

**功能**：设置工作表网格线。

**参数**：
- ws：要设置网格线的工作表对象

**执行流程**：
1. 激活工作表
2. 显示网格线

### 4.8 ApplyWorksheetProtection

```vba
Private Sub ApplyWorksheetProtection(ws As Worksheet)
```

**功能**：应用工作表保护。

**参数**：
- ws：要保护的工作表对象

**执行流程**：
1. 保护工作表，但允许用户选择单元格
2. 设置保护选项，限制用户修改

## 5. 时间轴生成详细说明

时间轴生成是UI模块的关键功能之一，它为甘特图提供了时间参考框架。

### 5.1 时间轴结构

时间轴由三行组成：
- **年份行（第3行）**：显示年份（yyyy）
- **月份行（第4行）**：显示月份（mm）
- **周数行（第5行）**：显示周数（cwxx），基于ISO 8601标准

系统支持三种时间单位：
- **日视图**：每一列代表一天，适合短期项目
- **周视图**：每一列代表一周，适合中期项目
- **月视图**：每一列代表一个月，适合长期项目

### 5.2 项目信息区域

项目信息区域包含：
- **项目名称（B1单元格）**：合并到时间轴右边界，左对齐显示
- **项目经理（B2单元格）**：合并到时间轴右边界，左对齐显示
- **最近更新（B3:B5单元格）**：合并单元格，显示最近更新日期，左对齐

### 5.3 坐标原点

时间轴生成过程中会确定甘特图的坐标原点，这是后续绘制任务和里程碑的基准点。坐标原点设置为周数行（第5行）的左上角单元格。

### 5.4 时间轴信息字典

GenerateTimeline函数返回的字典包含以下关键信息：
- **OriginRow**：坐标原点行号（第5行）
- **OriginCol**：坐标原点列号（C列）
- **TimeUnit**：时间单位（day/week/month）
- **StartDate**：开始日期
- **EndCol**：时间轴结束列号

这些信息将用于后续的任务和里程碑绘制过程。

## 6. 与其他模块的交互

UI模块主要与以下模块交互：

1. **Main模块**：接收来自Main模块的调用，准备甘特图工作表
2. **Data模块**：获取项目信息和配置数据
3. **Debug模块**：记录错误和调试信息
4. **Gantt模块**：协调甘特图生成，部分功能已移至Gantt模块
5. **Utilities模块**：使用通用工具函数，如日期处理
6. **ConfigDefaults模块**：获取配置项的默认值

## 7. 配置依赖

UI模块依赖于Config工作表中的以下配置，这些配置通过GetModuleConfig函数获取，并使用modConfigDefaults模块中的默认值：

### 7.1 UI模块配置

| ConfigName | 描述 | 默认值 |
|------------|------|--------|
| ProjectNameFont | 项目名称字体 | Arial |
| ProjectNameFontSize | 项目名称字体大小 | 16 |
| ProjectNameFontBold | 项目名称是否加粗 | True |
| ProjectNameFontColor | 项目名称字体颜色 | #000000 |
| ProjectManagerFont | 项目经理字体名称 | Arial |
| ProjectManagerFontSize | 项目经理字体大小 | 12 |
| ProjectManagerFontColor | 项目经理字体颜色 | #000000 |
| ChartTheme | 甘特图主题 | 1 |
| ChartGridlinesArea | 网格线应用区域 | all |
| ChartGridlinesType | 网格线类型 | all |
| ChartGridlineColor | 网格线颜色 | #DDDDDD |
| ChartBackgroundColor | 甘特图背景颜色 | #FFFFFF |
| ChartAlternateRowColor | 甘特图交替行颜色 | #F5F5F5 |
| ChartFont | 甘特图默认字体 | Barlow |
| ChartFontSize | 甘特图默认字号 | 10 |

### 7.2 Gantt模块配置

| ConfigName | 描述 | 默认值 |
|------------|------|--------|
| CellWidthFactor | 时间轴列宽系数 | 1.2 |
| DefaultTaskColor | 任务默认颜色 | #3366CC |
| DefaultMilestoneColor | 里程碑默认颜色 | #FF9900 |
| DefaultTaskPosition | 任务默认位置 | next |
| DefaultTaskTextPosition | 任务文本默认位置 | right |
| DefaultMilestoneTextPosition | 里程碑文本默认位置 | right |

### 7.3 配置访问方式

```vba
' 获取UI模块配置
Dim uiConfig As Dictionary
Set uiConfig = GetModuleConfig("UI", modConfigDefaults.GetUIDefaults())

' 获取Gantt模块配置
Dim ganttConfig As Dictionary
Set ganttConfig = GetModuleConfig("Gantt", modConfigDefaults.GetGanttDefaults())

' 使用配置
With ws.Cells(1, 2)
    .Font.Name = uiConfig("ProjectNameFont")
    .Font.Size = uiConfig("ProjectNameFontSize")
    .Font.Bold = uiConfig("ProjectNameFontBold")
End With

' 设置时间轴列宽
Dim cellWidthFactor As Double
cellWidthFactor = CDbl(ganttConfig("CellWidthFactor"))
ws.Columns("C:Z").columnWidth = 5 * cellWidthFactor
```

## 8. 流程图

### 8.1 甘特图工作表准备流程

```mermaid
flowchart TD
    Start([开始]) --> PrepareGanttWorksheet[准备甘特图工作表]

    PrepareGanttWorksheet --> CheckExistingSheet{存在旧工作表?}
    CheckExistingSheet -->|是| DeleteOldSheet[删除旧工作表]
    CheckExistingSheet -->|否| CreateNewSheet[创建新工作表]

    DeleteOldSheet --> CreateNewSheet
    CreateNewSheet --> FormatWorksheet[设置工作表格式]
    FormatWorksheet --> SetupHeaders[设置表头]

    SetupHeaders --> GenerateTimeline[生成时间轴]
    GenerateTimeline --> SetupGridlines[设置网格线]
    SetupGridlines --> End([结束])
```

### 8.2 时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> GenerateTimeline[生成时间轴]

    GenerateTimeline --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> GetConfig[获取配置]

    GetConfig --> DetermineTimeUnit[确定时间单位]
    DetermineTimeUnit --> SetOrigin[设置坐标原点]

    SetOrigin --> TimeUnitType{时间单位类型}
    TimeUnitType -->|日| GenerateDayView[生成日视图]
    TimeUnitType -->|周| GenerateWeekView[生成周视图]
    TimeUnitType -->|月| GenerateMonthView[生成月视图]

    GenerateDayView --> FormatTimeline[格式化时间轴]
    GenerateWeekView --> FormatTimeline
    GenerateMonthView --> FormatTimeline

    FormatTimeline --> ReturnTimelineInfo[返回时间轴信息]

    ReturnTimelineInfo --> End([结束])
```

## 9. 未来扩展

UI模块的未来扩展可能包括：

1. **交互式甘特图**：添加用户交互功能，允许用户直接在甘特图上调整任务
2. **时间轴缩放**：允许用户在不同时间单位之间切换
3. **自定义主题**：支持用户自定义甘特图的颜色和样式
4. **打印优化**：优化甘特图的打印布局
5. **多语言支持**：支持不同语言的界面显示
