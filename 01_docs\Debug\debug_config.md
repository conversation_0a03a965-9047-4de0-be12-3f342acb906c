# Debug模块配置说明

## 1. 配置参数概述

Debug模块提供了一系列配置参数，用于控制调试功能的行为。这些参数可以通过配置表进行设置，也可以在代码中通过`InitDebug`函数的参数直接指定。

## 2. 配置参数详情

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| DB001 | EnableDebug | False | 是否开启debug模式(True=开启，False=关闭) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("EnableDebug", False)获取，控制整个调试系统是否启用 |
| DB002 | DebugLevel | 4 | 调试级别(1=错误,2=警告,3=信息,4=详细) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("DebugLevel", 4)获取，默认为DEBUG_LEVEL_VERBOSE |
| DB003 | EnableFileLogging | False | 是否启用文件日志(True=启用，False=禁用) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("EnableFileLogging", False)获取，控制是否将日志写入文件 |
| DB004 | EnableImmediateOutput | False | 是否输出到即时窗口(True=输出，False=不输出) | Debug | Default | 在modDebug.WriteToLog中使用，控制是否将日志输出到VBE即时窗口 |
| DB005 | UTF8Encoding | True | 是否使用UTF-8编码日志文件(True=使用UTF-8，False=使用默认编码) | Debug | Default | 在modDebug模块中使用，确保日志文件支持中文等Unicode字符 |

## 3. 配置参数在代码中的使用

### 3.1 在modDebug.bas中的使用

```vba
' 初始化调试模块
Public Sub InitDebug(Optional level As Integer = -1, Optional enableDebug As Boolean = True, _
                    Optional enableFileLogging As Boolean = True, Optional enableImmediateOutput As Boolean = False, _
                    Optional logFilePath As String = "")
    On Error Resume Next

    ' 设置是否开启debug模式（如果未指定，使用配置值）
    IsDebugEnabled = IIf(enableDebug, True, CBool(GetConfig("EnableDebug", False)))

    ' 如果debug模式关闭，则直接退出
    If Not IsDebugEnabled Then
        Exit Sub
    End If

    ' 设置debug级别（如果未指定或指定为-1，使用配置值）
    If level = -1 Then
        DebugLevel = CInt(GetConfig("DebugLevel", 4))
    Else
        DebugLevel = level
    End If

    ' 初始化文件日志设置
    IsFileLoggingEnabled = enableFileLogging

    ' 初始化即时窗口输出设置
    IsImmediateOutputEnabled = enableImmediateOutput

    ' 设置UTF-8编码选项
    UTF8_ENCODING = CBool(GetConfig("UTF8Encoding", True))

    ' ...其他初始化代码...
End Sub
```

### 3.2 在modConfigDefaults.bas中的默认值定义

```vba
' 获取所有配置项的默认值
Public Function GetDefaults() As Dictionary
    Dim defaults As New Dictionary

    ' ...其他默认值...

    '===== Debug相关默认值 =====

    ' 是否开启debug模式
    defaults.Add "EnableDebug", False

    ' debug日志级别, 默认4 (DEBUG_LEVEL_VERBOSE)
    defaults.Add "DebugLevel", 4

    ' 是否启用文件日志
    defaults.Add "EnableFileLogging", False

    ' 是否输出到即时窗口
    defaults.Add "EnableImmediateOutput", False

    ' 文件编码设置
    defaults.Add "UTF8Encoding", True

    ' ...其他代码...

    Set GetDefaults = defaults
End Function
```

### 3.3 在modMain.bas中的使用示例

```vba
' Main function to generate Gantt chart
Public Sub GenerateGanttChart()
    On Error GoTo ErrorHandler

    ' 直接获取Debug模块配置参数
    Dim debugLevel As Integer
    Dim enableDebug As Boolean
    Dim enableFileLogging As Boolean
    Dim enableImmediateOutput As Boolean

    ' 获取配置值
    debugLevel = CInt(GetConfig("DebugLevel", 4))
    enableDebug = CBool(GetConfig("EnableDebug", False))
    enableFileLogging = CBool(GetConfig("EnableFileLogging", False))
    enableImmediateOutput = CBool(GetConfig("EnableImmediateOutput", False))

    ' 初始化调试系统，使用配置值
    modDebug.InitDebug _
        level:=debugLevel, _
        enableDebug:=enableDebug, _
        enableFileLogging:=enableFileLogging, _
        enableImmediateOutput:=enableImmediateOutput

    ' ...其他代码...
End Sub
```

## 4. 调试级别说明

Debug模块支持四个调试级别，通过常量定义：

```vba
' Debug level constants
Public Const DEBUG_LEVEL_ERROR As Integer = 1    ' 仅记录错误
Public Const DEBUG_LEVEL_WARNING As Integer = 2  ' 记录错误和警告
Public Const DEBUG_LEVEL_INFO As Integer = 3     ' 记录错误、警告和信息
Public Const DEBUG_LEVEL_VERBOSE As Integer = 4  ' 记录所有内容，包括详细跟踪
```

设置调试级别后，只有级别小于或等于当前设置的日志会被记录。例如，如果设置DebugLevel=2，则只会记录错误和警告，而信息和详细日志会被忽略。
