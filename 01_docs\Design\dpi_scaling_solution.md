# DPI缩放问题解决方案

## 问题描述

在多显示器环境下，特别是当主显示器和外接显示器具有不同DPI设置时，Excel VBA绘制的甘特图中的里程碑和任务条会出现位置错位问题。具体表现为：

1. **主显示器正常**：在主显示器上，里程碑和任务条能够正确居中显示在所在行中
2. **外接显示器错位**：在外接显示器上，里程碑和任务条会超出所在行的上下边界

## 根本原因

1. **DPI感知模式不一致**：Excel VBA在不同显示器上可能使用不同的DPI感知模式
2. **坐标系计算未考虑DPI差异**：现有的坐标计算代码没有考虑多显示器环境下的DPI差异
3. **行高和单元格尺寸变化**：外接显示器的DPI设置导致Excel单元格的实际像素尺寸发生变化

## 解决方案

### 1. 添加DPI感知功能

在`modUtilities.bas`中添加了以下函数：

#### GetDPIScaleFactor()
- 检测当前显示器的DPI设置
- 计算相对于标准96 DPI的缩放因子
- 支持配置开关和调整因子

#### GetCurrentMonitorInfo()
- 获取当前显示器的详细信息
- 包括分辨率、工作区域、是否为主显示器等

### 2. 修改坐标计算逻辑

#### 里程碑绘制修正
在`DrawMilestone`函数中：
```vba
' 计算Y坐标 - 使用行的中心点（考虑DPI缩放）
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).height

' 获取DPI缩放因子以确保在不同显示器上的准确定位
Dim dpiScaleFactor As Double
dpiScaleFactor = GetDPIScaleFactor()

' 应用DPI缩放修正
milestoneY = ws.Cells(row, 1).top + (rowHeight / 2) * dpiScaleFactor
```

#### 任务条绘制修正
在`DrawTask`函数中：
```vba
' 计算任务条的垂直居中位置（考虑DPI缩放）
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).height

' 获取DPI缩放因子以确保在不同显示器上的准确定位
Dim dpiScaleFactor As Double
dpiScaleFactor = GetDPIScaleFactor()

' 应用DPI缩放修正
top = ws.Cells(row, 1).top + ((rowHeight - height) / 2) * dpiScaleFactor
```

### 3. 配置项

在`modConfigDefaults.bas`中添加了以下配置项：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| EnableDPICorrection | True | 是否启用DPI缩放修正 |
| DPIAdjustmentFactor | 1.0 | DPI调整因子，用于微调 |

### 4. 测试功能

添加了`TestDPIScaling()`函数用于测试DPI缩放功能：
- 检测当前DPI缩放因子
- 获取显示器信息
- 显示配置状态
- 提供详细的调试日志

## 使用方法

### 1. 启用DPI修正
DPI修正功能默认启用。如需禁用，可在配置表中设置：
```
ConfigName: EnableDPICorrection
Value: False
```

### 2. 调整DPI因子
如果默认的DPI修正效果不理想，可以调整DPI因子：
```
ConfigName: DPIAdjustmentFactor
Value: 0.9  # 或其他合适的值
```

### 3. 测试DPI功能
在VBA编辑器中运行：
```vba
Call modUtilities.TestDPIScaling
```

## 技术细节

### Windows API调用
使用以下Windows API获取DPI信息：
- `GetDC` / `ReleaseDC`：获取和释放设备上下文
- `GetDeviceCaps`：获取设备能力，包括DPI
- `GetActiveWindow`：获取当前活动窗口
- `MonitorFromWindow`：获取窗口所在的显示器
- `GetMonitorInfo`：获取显示器详细信息

### 兼容性
- 支持VBA7和早期版本
- 使用条件编译确保在不同Office版本中正常工作
- 提供错误处理和默认值机制

### 性能考虑
- DPI检测结果可以缓存以提高性能
- 仅在需要时调用Windows API
- 提供配置开关允许禁用功能

## 故障排除

### 1. 如果DPI修正无效
1. 检查`EnableDPICorrection`配置是否为True
2. 运行`TestDPIScaling()`查看DPI检测结果
3. 尝试调整`DPIAdjustmentFactor`值

### 2. 如果出现错误
1. 检查调试日志中的错误信息
2. 确认Windows API调用是否成功
3. 验证Excel版本兼容性

### 3. 微调位置
如果位置仍有轻微偏差，可以：
1. 调整`DPIAdjustmentFactor`配置项
2. 根据具体显示器特性进行微调
3. 考虑添加显示器特定的配置

## 未来改进

1. **缓存DPI信息**：避免重复检测相同显示器的DPI
2. **显示器特定配置**：为不同显示器提供独立的调整因子
3. **自动校准**：提供自动校准功能，让用户手动调整位置后自动计算最佳因子
4. **更精确的检测**：使用更高级的Windows API获取更精确的DPI信息

## 相关文件

- `02_code\Debug\modUtilities.bas`：DPI相关函数
- `02_code\Debug\modGantt.bas`：修改的绘制函数
- `02_code\Debug\modConfigDefaults.bas`：配置默认值
- `01_docs\design\dpi_scaling_solution.md`：本文档
