# DrawTask 函数说明文档

## 功能概述

`DrawTask` 函数是甘特图系统中的核心绘图函数之一，负责在甘特图工作表上绘制任务条形图形。该函数根据任务的开始日期和结束日期，在指定行位置创建矩形形状表示任务，并根据任务进度添加进度条，同时添加任务描述标签。

## 函数签名

```vba
Private Sub DrawTask(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含任务信息的字典对象，必须包含以下键：<br>- StartDate：任务开始日期<br>- EndDate：任务结束日期<br>- Color：任务颜色（十六进制格式，如"#FF0000"）<br>- Progress：任务进度（0-1之间的小数）<br>- Description：任务描述（可选）<br>- ID：任务ID（可选，用于日志记录） |
| row | Long | 任务在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息，包含以下键：<br>- OriginX：坐标系原点的X坐标<br>- Width：坐标系的总宽度<br>- StartDate：坐标系的起始日期<br>- EndDate：坐标系的结束日期<br>- TotalDays：坐标系覆盖的总天数 |

## 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 任务条高度（像素） |
| GT027 | TaskBarBorderWidth | 0 | 任务条边框宽度（0=无边框） |
| GT028 | ProgressBarColor | #66CC66 | 进度条颜色 |
| GT029 | LabelDistance | 5 | 标签与任务条的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

## 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 任务条形状（矩形）
2. 进度条形状（如果任务有进度）
3. 任务描述标签（文本框）

## 处理流程

### 1. 初始化和日志记录

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawTask", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 记录任务日期信息
Dim startDateStr As String, endDateStr As String
startDateStr = Format(task("StartDate"), "yyyy-mm-dd")
endDateStr = Format(task("EndDate"), "yyyy-mm-dd")
modDebug.LogVerbose "任务日期范围 - 开始: " & startDateStr & ", 结束: " & endDateStr, "modGantt.DrawTask"

Dim ws As Worksheet
Set ws = ThisWorkbook.Worksheets("GanttChart")
```

这部分代码初始化函数并记录详细的日志信息，包括任务ID、描述、行位置和日期范围。

### 2. 获取配置参数

```vba
' 获取配置参数
Dim taskBarHeight As Long
taskBarHeight = Val(GetConfigValue("GT026", "11")) ' 默认任务条高度为11

Dim taskBarBorderWidth As Single
taskBarBorderWidth = Val(GetConfigValue("GT027", "0")) ' 默认无边框

Dim progressBarColor As String
progressBarColor = GetConfigValue("GT028", "#66CC66") ' 默认进度条颜色为绿色

Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT029", "5")) ' 默认标签距离为5像素

Dim rowPadding As Long
rowPadding = Val(GetConfigValue("GT030", "3")) ' 默认行高上下预留3像素
```

这部分代码获取函数所需的配置参数：
1. 任务条高度（默认11像素）
2. 任务条边框宽度（默认0，表示无边框）
3. 进度条颜色（默认绿色 #66CC66）
4. 标签与任务条的距离（默认5像素）
5. 行高上下预留空隙（默认3像素）

### 3. 计算任务条位置和大小

```vba
' 计算任务条的位置和大小
Dim left As Double, top As Double, width As Double, height As Double

' 计算任务开始和结束的X坐标
Dim startX As Double, endX As Double
startX = CalculateXCoordinate(task("StartDate"), timelineCoords)
endX = CalculateXCoordinate(task("EndDate"), timelineCoords)

' 计算任务条的左边缘、宽度和高度
left = startX
width = endX - startX
height = taskBarHeight ' 使用配置的任务条高度

' 计算任务条的垂直居中位置
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
top = ws.Cells(row, 1).Top + (rowHeight - height) / 2 ' 垂直居中
```

这部分代码计算任务条的位置和大小：
1. 使用 `CalculateXCoordinate` 函数将任务的开始日期和结束日期转换为X坐标
2. 计算任务条的左边缘（startX）、宽度（endX - startX）和高度（使用配置的任务条高度）
3. 计算任务条的垂直位置，使其在行内垂直居中

### 4. 创建任务条形状

```vba
' 创建任务条形状
Dim taskShape As shape

On Error Resume Next
Set taskShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, width, height)

If Err.Number <> 0 Then
    modDebug.LogError Err.Number, "创建任务条形状失败 - " & Err.description, "modGantt.DrawTask"
    modDebug.LogFunctionExit "modGantt.DrawTask", "失败 - 无法创建任务条形状"
    Exit Sub
End If

On Error GoTo ErrorHandler
```

这部分代码创建任务条形状（矩形），并处理可能的错误：
1. 使用 `AddShape` 方法创建矩形形状
2. 如果创建失败，记录错误并退出函数

### 5. 设置任务条格式

```vba
' 设置任务条格式
Dim taskColor As String
taskColor = task("Color")

taskShape.Fill.ForeColor.RGB = GetRGBColor(taskColor)

' 设置任务条边框
If taskBarBorderWidth = 0 Then
    taskShape.Line.Visible = msoFalse ' 无边框
Else
    taskShape.Line.Visible = msoTrue
    taskShape.Line.Weight = taskBarBorderWidth
    taskShape.Line.ForeColor.RGB = GetRGBColor(taskColor)
End If
```

这部分代码设置任务条的颜色和边框：
1. 获取任务的颜色（十六进制格式）
2. 使用 `GetRGBColor` 函数将十六进制颜色转换为RGB值
3. 设置任务条的填充颜色
4. 根据配置参数设置任务条边框：
   - 如果边框宽度为0，则不显示边框
   - 如果边框宽度大于0，则设置边框宽度和颜色

### 6. 绘制进度条（如果有进度）

```vba
' 如果有进度，绘制进度条
Dim progressShape As Shape
Set progressShape = Nothing

If task("Progress") > 0 Then
    Dim progressWidth As Double
    progressWidth = width * task("Progress")

    On Error Resume Next
    Set progressShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, progressWidth, height)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建进度条形状失败 - " & Err.description, "modGantt.DrawTask"
    Else
        progressShape.Fill.ForeColor.RGB = GetRGBColor(progressBarColor)

        ' 设置进度条边框与任务条一致
        If taskBarBorderWidth = 0 Then
            progressShape.Line.Visible = msoFalse ' 无边框
        Else
            progressShape.Line.Visible = msoTrue
            progressShape.Line.Weight = taskBarBorderWidth
            progressShape.Line.ForeColor.RGB = GetRGBColor(taskColor)
        End If

        ' 将进度条置于最顶层
        progressShape.ZOrder msoBringToFront
    End If

    On Error GoTo ErrorHandler
End If
```

这部分代码根据任务进度绘制进度条：
1. 如果任务进度大于0，计算进度条宽度（任务条宽度 * 进度）
2. 创建进度条形状（矩形）
3. 设置进度条颜色（使用配置参数 GT028，默认为绿色 #66CC66）
4. 设置进度条边框与任务条一致：
   - 如果边框宽度为0，则不显示边框
   - 如果边框宽度大于0，则设置边框宽度和颜色
5. 将进度条置于最顶层，确保进度条不被任务条遮挡

### 7. 添加任务描述标签

```vba
' 添加任务描述标签（使用坐标系）
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, taskShape, startX + width / 2, top + height / 2, height, labelDistance)

If Not labelShape Is Nothing Then
    ' 动态调整行高以适应任务和标签，考虑上下预留空隙
    AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
Else
    modDebug.LogWarning "任务标签创建失败或返回为Nothing", "modGantt.DrawTask"
End If
```

这部分代码添加任务描述标签并调整行高：
1. 调用 `AddTaskLabelWithCoordinates2` 函数创建任务描述标签，传递标签距离参数
2. 如果标签创建成功，调用 `AdjustRowHeightWithPadding` 函数动态调整行高，确保任务条和标签能够完全显示，并考虑上下预留空隙

### 8. 错误处理

```vba
' 记录函数退出
modDebug.LogFunctionExit "modGantt.DrawTask", "成功"
Exit Sub

ErrorHandler:
modDebug.LogError Err.Number, Err.Description, "modGantt.DrawTask"
modDebug.LogFunctionExit "modGantt.DrawTask", "失败 - " & Err.Description
Err.Raise Err.Number, "modGantt.DrawTask", Err.Description
```

这部分代码处理函数执行过程中可能发生的错误：
1. 如果函数正常执行完成，记录成功退出
2. 如果发生错误，记录错误信息并重新引发错误

## 流程图

```mermaid
flowchart TD
    A[开始] --> B[初始化和日志记录]
    B --> C[获取配置参数]
    C --> D[获取GanttChart工作表引用]
    D --> E[计算任务条位置和大小]
    E --> F[创建任务条形状]
    F --> G{创建成功?}
    G -->|否| H[记录错误并退出]
    G -->|是| I[设置任务条填充颜色]
    I --> J{边框宽度为0?}
    J -->|是| K[设置无边框]
    J -->|否| L[设置边框宽度和颜色]
    K --> M{任务有进度?}
    L --> M
    M -->|是| N[计算进度条宽度]
    N --> O[创建进度条形状]
    O --> P{创建成功?}
    P -->|否| Q[记录错误]
    P -->|是| R[设置进度条填充颜色]
    R --> S{边框宽度为0?}
    S -->|是| T[设置无边框]
    S -->|否| U[设置边框宽度和颜色]
    T --> V[将进度条置于最顶层]
    U --> V
    M -->|否| W[添加任务描述标签]
    Q --> W
    V --> W
    W --> X{标签创建成功?}
    X -->|否| Y[记录警告]
    X -->|是| Z[动态调整行高，考虑预留空隙]
    Z --> AA[记录函数退出]
    Y --> AA
    AA --> AB[结束]
    H --> AB
```

## 相关函数

### CalculateXCoordinate 函数

```vba
Private Function CalculateXCoordinate(targetDate As Date, coords As Dictionary) As Double
    ' 计算日期与起始日期的天数差
    Dim daysDiff As Long
    daysDiff = DateDiff("d", coords("StartDate"), targetDate)

    ' 计算X坐标
    CalculateXCoordinate = coords("OriginX") + (coords("Width") * daysDiff / coords("TotalDays"))
End Function
```

这个函数将日期转换为X坐标：
1. 计算目标日期与坐标系起始日期的天数差
2. 根据天数差在总天数中的比例计算X坐标

### AddTaskLabelWithCoordinates2 函数

这个函数是 AddTaskLabelWithCoordinates 的改进版，负责创建任务描述标签，并将其放置在任务条附近。主要功能：
1. 创建临时文本框以计算文本尺寸
2. 根据文本尺寸和任务条尺寸确定标签位置和对齐方式：
   - 如果标签能放在任务条内部，则居中对齐
   - 如果标签放在任务条右侧，则左对齐
   - 如果标签放在任务条左侧，则右对齐
   - 如果标签放在任务条上方或下方，则左对齐
3. 创建文本框形状并设置文本内容和格式
4. 根据配置的标签距离参数调整标签位置

### AdjustRowHeightWithPadding 函数

这个函数是 AdjustRowHeight 的改进版，根据任务条和标签的大小动态调整行高，确保所有元素都能完全显示。主要功能：
1. 计算任务条和标签占用的总高度
2. 添加上下预留空隙（使用配置参数）
3. 如果总高度大于当前行高，调整行高以适应所有元素
4. 确保行高不小于最小值

## 注意事项

1. **必需的任务属性**：
   - StartDate：任务开始日期
   - EndDate：任务结束日期
   - Color：任务颜色（十六进制格式）
   - Progress：任务进度（0-1之间的小数）

2. **可选的任务属性**：
   - Description：任务描述（用于标签）
   - ID：任务ID（用于日志记录）

3. **坐标系要求**：
   - timelineCoords 字典必须包含所有必需的键（OriginX, Width, StartDate, EndDate, TotalDays）
   - 坐标系的日期范围应该包含任务的日期范围

4. **错误处理**：
   - 函数使用 On Error Resume Next 单独处理形状创建错误
   - 如果任务条创建失败，函数会退出
   - 如果进度条创建失败，函数会继续执行，但记录错误

5. **配置参数**：
   - 任务条高度、边框宽度、进度条颜色、标签距离和行高预留空隙都是可配置的
   - 这些参数可以在Config工作表中设置，也可以通过代码动态修改

6. **性能考虑**：
   - 创建形状是一个相对耗时的操作，特别是在任务数量较多时
   - 函数使用详细的日志记录，有助于调试但可能影响性能

7. **动态行高调整**：
   - 函数会根据任务条和标签的大小动态调整行高，并考虑上下预留空隙
   - 这确保了所有元素都能完全显示，但可能导致行高不一致

8. **标签位置和对齐**：
   - 标签位置会根据任务条大小和文本长度智能调整
   - 标签对齐方式会根据位置自动设置：任务条内部居中，右侧左对齐，左侧右对齐

## 使用示例

在 `DrawTasksAndMilestones` 函数中的调用示例：

```vba
' 第三步：根据任务类型绘制任务或里程碑
If task.Exists("Type") Then
    If task("Type") = "A" Then
        ' 绘制任务条
        DrawTask task, taskRow, timelineCoords
    ElseIf task("Type") = "M" Then
        ' 绘制里程碑
        DrawMilestone task, taskRow, timelineCoords
    Else
        modDebug.LogWarning "任务类型无效: " & task("Type") & "，跳过绘制", "modGantt.DrawTasksAndMilestones"
    End If
Else
    modDebug.LogWarning "任务缺少Type属性，跳过绘制", "modGantt.DrawTasksAndMilestones"
End If
```

## 与其他函数的关系

- **与DrawTasksAndMilestones的关系**：
  - `DrawTasksAndMilestones`调用`DrawTask`绘制类型为"A"的任务
  - 在调用前，`DrawTasksAndMilestones`已经确定了任务的行位置
  - 传递时间轴坐标系信息，避免重复计算

- **与DrawMilestone的关系**：
  - `DrawTask`和`DrawMilestone`是并列的函数，分别处理不同类型的任务
  - 两个函数都使用相同的坐标系统和标签添加机制
  - `DrawTask`处理有持续时间的任务（开始日期到结束日期），而`DrawMilestone`处理单一日期的里程碑

- **与坐标系统的关系**：
  - `DrawTask`依赖于预先建立的时间轴坐标系统
  - 使用`CalculateXCoordinate`函数将日期转换为X坐标
  - 坐标系统确保了任务在时间轴上的准确定位
