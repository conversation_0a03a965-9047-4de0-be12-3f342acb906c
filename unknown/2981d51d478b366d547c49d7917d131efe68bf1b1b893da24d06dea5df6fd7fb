# 变更记录

## 版本 1.1.0 (2024-06-01)

### 新增功能

1. **代码国际化**
   - 将所有代码模块中的中文注释和消息更新为英文，避免VBA编辑器中的编码问题
   - 更新了用户界面按钮和消息，使用英文显示

### 改进功能

1. **错误处理**
   - 统一使用modDebug.LogError进行错误记录，替代直接的错误记录方法
   - 改进了错误消息的格式和内容，提高可读性

### 文档更新

1. **设计文档**
   - 更新了所有设计文档，确保与代码逻辑一致
   - 添加了新的设计文档：ThisWorkbook_design.md, modDebug_design.md, modDebugTools_design.md
   - 在所有设计文档中添加了关于代码注释和消息已更新为英文的说明
   - 保持设计文档使用中文，以便于中文用户理解

2. **项目结构文档**
   - 更新了project_structure.md，添加了modDebugTools模块
   - 更新了模块功能描述，明确了各模块的职责

3. **架构文档**
   - 更新了architecture.md中的模块依赖关系图
   - 添加了DebugTools模块的详细说明

## 版本 1.0.0 (2023-04-20)

### 新增功能

1. **调试模块 (modDebug)**
   - 添加了新的调试模块，提供全面的日志记录和错误跟踪功能
   - 实现了日志文件输出，支持实时记录系统运行状态
   - 添加了不同级别的日志记录（信息、警告、错误）
   - 添加了函数进入和退出的跟踪功能
   - 实现了日志文件重新初始化功能，每次生成甘特图时创建新的日志文件

### 改进功能

1. **甘特图生成 (modGantt)**
   - 重写了日期到列位置的转换算法 (GetColumnFromDate)，使用线性插值方法提高准确性
   - 改进了任务和里程碑的绘制逻辑，确保正确定位在时间轴上
   - 添加了对任务宽度的检查，确保宽度为正值
   - 增强了对列位置超出时间轴范围的处理
   - 修改了DrawTask和DrawMilestone函数的签名，移除了不再使用的参数
   - 添加了更详细的错误处理和调试信息

2. **主模块 (modMain)**
   - 更新了GenerateGanttChart函数，添加了对调试日志的重新初始化

### 文档更新

1. **设计文档**
   - 更新了modGantt_design.md，反映最新的代码逻辑和函数签名
   - 更新了甘特图生成流程图，显示正确的执行顺序
   - 添加了对新增函数的详细说明
   - 添加了最新改进部分，描述了对算法和功能的改进

2. **项目结构文档**
   - 更新了project_structure.md，添加了modDebug模块
   - 更新了模块功能描述，明确了各模块的职责

3. **架构文档**
   - 更新了architecture.md中的模块依赖关系图
   - 添加了Debug模块的详细说明
   - 更新了业务逻辑层的类图和模块组织图

### 修复问题

1. **甘特图绘制问题**
   - 修复了任务和里程碑位置计算错误，确保它们正确显示在对应的日期位置
   - 修复了跨月周的处理逻辑，确保正确显示
   - 解决了timeRange变量未定义的问题

2. **调试功能**
   - 修复了日志文件不会重置的问题，现在每次生成甘特图时都会创建新的日志文件

### 技术债务

1. **代码重构**
   - 将错误处理功能从modUtilities模块移至modDebug模块
   - 重构了GetColumnFromDate函数，提高了代码可读性和可维护性

### 依赖关系变更

1. **模块依赖**
   - 添加了对modDebug模块的依赖
   - 更新了模块间的调用关系，确保正确的依赖顺序

## 下一步计划

1. 进一步优化甘特图的绘制性能
2. 增强用户界面交互功能
3. 添加更多的自定义选项
4. 完善错误处理和异常情况的处理
