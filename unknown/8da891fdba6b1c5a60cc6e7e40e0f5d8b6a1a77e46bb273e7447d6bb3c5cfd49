Sub CreateGanttChartHeader_ISOWEEKNUM()
    ' 声明变量
    Dim ws As Worksheet
    Dim startDate As Date, endDate As Date, pj_startDate As Date, pj_endDate As Date
    Dim switchDate As Date
    Dim headerRange As Range
    Dim lastMonth As Integer, lastYear As Integer
    Dim monthStart As Range, yearStart As Range
    Dim col_Width_factor As Single

    ' 检查当前工作簿中是否存在名为 "Plan" 的工作表
    For Each ws In ThisWorkbook.Sheets
        ' 如果找到 "Plan" 工作表
        If ws.Name = "Plan" Then
            ' 关闭警告提示（例如删除确认）
            Application.DisplayAlerts = False
            ' 删除工作表
            ws.Delete
            ' 重新开启警告提示
            Application.DisplayAlerts = True
            ' 退出循环，因为已经找到并处理了
            Exit For
        End If
    Next ws

    ' 创建一个新的工作表，并放在所有现有工作表的最后
    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ' 将新工作表命名为 "Plan"
    ws.Name = "Plan"

    ' 将新工作表的所有单元格背景色设置为白色
    ws.Cells.Interior.Color = RGB(255, 255, 255)
    ' 设置新工作表所有单元格的文字水平居中对齐
    ws.Cells.HorizontalAlignment = xlCenter

    ' 设置甘特图表头的起始位置在 B1 单元格
    Set headerRange = ws.Range("B1")
      Debug.Print "initial  headerRange is " & headerRange.Address
    ' 硬编码设置项目的开始日期和结束日期
    pj_startDate = DateValue("12/21/2023") ' 示例日期
    pj_endDate = DateValue("2/1/2024")   ' 示例日期
    ' 设置列宽的调整系数，用于让列宽与天数挂钩
    col_Width_factor = 1

    ' 计算生成日历表头的起始日期（项目开始日所在月份的第一天）
    startDate = DateSerial(Year(pj_startDate), Month(pj_startDate), 1)
    ' 计算生成日历表头的结束日期（项目结束日所在月份的最后一天）
    endDate = DateSerial(Year(pj_endDate), Month(pj_endDate) + 1, 0)

    ' 初始化循环日期为日历范围起始日期 (startDate) 所在日历周（周一为一周开始）的星期一
    switchDate = startDate + 1 - Weekday(startDate, vbMonday)

    ' 循环遍历，以周为单位生成表头列，直到超过结束日期
    Do While switchDate <= endDate
        ' 在当前的 headerRange 位置进行操作
        With headerRange
            ' 判断当前周 (switchDate 到 switchDate + 6) 是否跨越月份边界
            If Month(switchDate) <> Month(switchDate + 6) Then
                ' 处理跨月情况下的第一个周（如果项目开始日在该跨月周的后半段，且 switchDate 是周一，但 startDate 在那周中间）
                If (Month(switchDate) Mod 12) + 1 = Month(startDate) And switchDate < startDate Then
                    ' 计算并写入跨月周后半段（下一月部分）的 ISO 周数到相对于 headerRange 向下偏移4行、向右偏移1列的单元格 (即表头第5行，当前列右边一列)
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate + 6)
                    .Offset(4, 1).NumberFormat = "00" ' 格式化周数为两位数字

                    ' 写入跨月周后半段的月份到表头第4行，当前列右边一列
                    .Offset(3, 1).Value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00" ' 格式化月份为两位数字

                    ' 写入跨月周后半段的年份到表头第3行，当前列右边一列
                    .Offset(2, 1).Value = Year(switchDate + 6)
                    ' 设置该列的宽度，根据项目开始日到周日的天数乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    ' 将 headerRange 向右移动一列，准备写入下一列数据
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    Debug.Print "the first blood (split, part B) in CW " & WorksheetFunction.IsoWeekNum(switchDate + 6) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                    Debug.Print "headerRange is " & headerRange.Address


                ' 处理跨月情况下的最后一个周（如果项目结束日在一个不完整的周内，并且该周跨月）
                ElseIf switchDate = endDate - Weekday(endDate, vbMonday) + 1 Then
                    ' 计算并写入跨月周前半段（当前月部分）的 ISO 周数到相对于 headerRange 向下偏移4行、当前列的单元格 (即表头第5行)
                    .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 0).NumberFormat = "00"

                    ' 写入跨月周前半段的月份到表头第4行，当前列
                    .Offset(3, 0).Value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"

                    ' 写入跨月周前半段的年份到表头第3行，当前列
                    .Offset(2, 0).Value = Year(switchDate)
                    ' 设置该列的宽度，根据项目结束日在周内是第几天乘以系数
                    .EntireColumn.ColumnWidth = Weekday(endDate, vbMonday) * col_Width_factor
                    ' Debug输出信息
                    Debug.Print "the last blood in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: " & Weekday(endDate, vbMonday)

                ' 处理中间跨月的完整周（将该周拆分为两列显示，一列显示当前月部分，一列显示下一月部分）
                ElseIf switchDate > startDate And switchDate < endDate - Weekday(endDate, vbMonday) + 1 Then
                    ' 计算并写入当前月剩余天数所在的周的 ISO 周数到当前列的表头第5行 (周数)
                    ' 注意：此处使用下个月第一天计算周数，逻辑特殊，可能意图是该跨月周的整体周数
                    .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(DateSerial(Year(switchDate), Month(switchDate) + 1, 0))
                    .Offset(4, 0).NumberFormat = "00"

                    ' 写入当前月剩余天数所在周的月份到当前列的表头第4行 (月份)
                    .Offset(3, 0).Value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"
                    ' 设置当前列的宽度，可能基于当前月在该周的天数乘以系数
                    .Offset(4, 0).EntireColumn.ColumnWidth = Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) * col_Width_factor
                    ' 写入当前月剩余天数所在周的年份到当前列的表头第3行 (年份)
                    .Offset(2, 0).Value = Year(switchDate)

            Debug.Print "Mix--------------------killing_A in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: " & Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)
                    ' 计算并写入下一个月开始天数所在的周的 ISO 周数到右边一列的表头第5行 (周数)
                    ' 注意：此处使用周日 (switchDate + 6) 计算周数
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate + 6)
                    .Offset(4, 1).NumberFormat = "00"

                    ' 写入下一个月开始天数所在的周的月份到右边一列的表头第4行 (月份)
                    .Offset(3, 1).Value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00"

                    ' 写入下一个月开始天数所在的周的年份到右边一列的表头第3行 (年份)
                    .Offset(2, 1).Value = Year(switchDate + 6)
                    ' 设置右边一列的宽度，可能基于下个月在该周的天数乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) * col_Width_factor
                    ' 将 headerRange 向右移动一列（处理完跨月周的第二部分后移动）
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    Debug.Print "headerRange is " & headerRange.Address
                    Debug.Print "Mix----------------------------killing_B in CW " & WorksheetFunction.IsoWeekNum(switchDate + 6) & ", DAYS: " & (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday))

                End If
            ' 处理不跨月情况下的第一个周（项目开始日期落在当前 switchDate 所在的周内，且该周不跨月）
            ElseIf switchDate <= startDate And startDate <= switchDate + 6 Then
                 ' 确保项目开始日与当前周在同一个月份
                 If Month(switchDate) = Month(startDate) Then
                    ' 计算并写入第一周（从项目开始日到周日的不完整周）的 ISO 周数到当前列的表头第5行
                    ' 使用项目开始日计算周数、月份和年份
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(startDate)
                    .Offset(4, 1).NumberFormat = "00"

                    .Offset(3, 1).Value = Month(startDate)
                    .Offset(3, 1).NumberFormat = "00"

                    .Offset(2, 1).Value = Year(startDate)
                    ' 设置该列的宽度，根据项目开始日到该周末的天数乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    ' Debug输出信息
                    Debug.Print "the first blood (not split) in CW " & WorksheetFunction.IsoWeekNum(startDate) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                                        Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    Debug.Print "Perfect-shot (at beginning, year crossover) in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"
                 Else ' 如果是跨年但不跨月的第一周（例如年底最后一周跨年到下一年1月）
                     ' 写入完整周的周、月、年信息到当前列
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 1).NumberFormat = "00"

                    .Offset(3, 1).Value = Month(switchDate)
                    .Offset(3, 1).NumberFormat = "00"

                    .Offset(2, 1).Value = Year(switchDate)
                    ' 设置列宽为标准宽度（7天乘以系数）
                    .EntireColumn.ColumnWidth = 7 * col_Width_factor
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    Debug.Print "Perfect-shot (at beginning, year crossover) in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"

                    Debug.Print "2nd headerRange is " & headerRange.Address
                 End If

            ' 处理不跨月且不是第一周或最后一-周的完整周
            Else
                ' 计算并写入完整周的 ISO 周数到当前列的表头第5行
                .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(switchDate)
                .Offset(4, 0).NumberFormat = "00"

                ' 写入完整周的月份到当前列的表头第4行
                .Offset(3, 0).Value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                ' 写入完整周的年份到当前列的表头第3行
                .Offset(2, 0).Value = Year(switchDate)
                ' 设置列宽为标准宽度（7天乘以系数）
                .EntireColumn.ColumnWidth = 7 * col_Width_factor
                ' Debug输出信息
                Debug.Print "Perfect-shot in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"
            End If

        End With


        Set headerRange = headerRange.Offset(0, 1)
   Debug.Print "next loop headerRange is " & headerRange.Address
        ' 将 switchDate 推进到下一个星期的星期一，准备处理下一周
        switchDate = switchDate + 7

    Loop ' 循环结束

    ' 声明合并相关的变量
    Dim lastCol As Long
    Dim i As Integer, j As Integer
    Dim mergeRange As Range

    ' 确定表头数据写入的最右边的列（以表头第3行，即 B1 偏移 2 行的行作为参考）
    lastCol = ws.Cells(3, ws.Columns.Count).End(xlToLeft).Column

    ' 关闭警告消息，以便进行单元格合并操作
    Application.DisplayAlerts = False

    ' 循环遍历需要合并的表头行：表头第3行（年份）、第4行（月份）、第5行（周数）
    ' 这些行对应于 headerRange (B1) 向下偏移 2, 3, 4 的行，即工作表的第 3, 4, 5 行
    For i = 3 To 5
        ' 重置合并范围
        Set mergeRange = Nothing
        ' 从第3列 (C列，因为 headerRange 是 B1，第一个数据列从 B 列开始，合并从下一列 C 列开始) 开始遍历到最后一列
        For j = 3 To lastCol
            ' 如果当前合并范围不为空，并且当前单元格的值与前一个单元格的值相同，并且当前单元格没有被合并
            If Not mergeRange Is Nothing And ws.Cells(i, j).Value = ws.Cells(i, j - 1).Value And Not ws.Cells(i, j).MergeCells Then
                ' 将当前单元格添加到当前的合并范围中
                Set mergeRange = ws.Range(mergeRange, ws.Cells(i, j))
            Else
                ' 如果当前单元格与前一个单元格的值不同，或者开始一个新的合并范围
                ' 如果之前的合并范围不为空，则进行合并
                If Not mergeRange Is Nothing Then
                    ' 如果合并范围包含多于一个单元格，则执行合并
                    If mergeRange.Columns.Count > 1 Then
                        mergeRange.Merge
                        ' 可选：为合并后的单元格添加格式（如边框）
                        ' mergeRange.Borders.LineStyle = xlThin
                    End If
                End If
                ' 将当前的单元格设置为新的合并范围的起始
                Set mergeRange = ws.Cells(i, j)
            End If
        Next j
        ' 循环结束后，检查并合并最后一个累积的合并范围
        If Not mergeRange Is Nothing And mergeRange.Columns.Count > 1 Then
            mergeRange.Merge
            ' 可选：为合并后的单元格添加格式
            ' mergeRange.Borders.LineStyle = xlThin
        End If
    Next i

    ' 重新开启警告消息
    Application.DisplayAlerts = True

    ' 可选：为表头区域添加边框并设置字体样式
    ' 边框范围从 C3 单元格到第5行的最后一列
     ws.Range("C3", ws.Cells(5, lastCol)).Borders.LineStyle = xlContinuous
     ' 设置字体不加粗 (根据您提供的代码修改，原始代码有 Bold = True 的注释)
     ws.Range("C3", ws.Cells(5, lastCol)).Font.Bold = False

End Sub

Sub CreateGanttChartHeader_ISOWEEKNUM_DebugStrict() ' 更改子程序名称以明确目的
    Dim ws As Worksheet
    Dim startDate As Date, endDate As Date, pj_startDate As Date, pj_endDate As Date
    Dim switchDate As Date
    Dim headerRange As Range
    Dim lastMonth As Integer, lastYear As Integer ' 这些变量在原代码中声明但未使用
    Dim monthStart As Range, yearStart As Range ' 这些变量在原代码中声明但未使用
    Dim col_Width_factor As Single

    ' --- Debug 输出相关的辅助变量 ---
    Dim isFirstLoopIteration As Boolean
    Dim previousLoopYear As Integer
    Dim currentWeekNum As Variant ' 用于存储 WeekNum 计算结果，方便 Debug 输出

    Debug.Print "================================================================================"
    Debug.Print ">>> 宏 'CreateGanttChartHeader_DebugStrict' 开始执行 <<<"
    Debug.Print "================================================================================"

    ' 检查当前工作簿中是否存在名为 "Plan" 的工作表，如果存在则删除
    Debug.Print Chr(10) & "--- 步骤: 检查并删除现有工作表 'Plan' ---"
    Dim foundPlanSheet As Boolean
    foundPlanSheet = False
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Plan" Then
            Debug.Print "  - 发现工作表 'Plan'。正在执行删除操作..."
            Application.DisplayAlerts = False ' 关闭警告提示
            ws.Delete
            Application.DisplayAlerts = True ' 重新开启警告提示
            Debug.Print "  - 工作表 'Plan' 已成功删除。"
            foundPlanSheet = True
            Exit For
        End If
    Next ws
    If Not foundPlanSheet Then
        Debug.Print "  - 未找到名为 'Plan' 的工作表，无需删除。"
    End If
    Debug.Print "--- 'Plan' 工作表检查及删除步骤完成 ---"

    ' 创建一个新的工作表，并放在所有现有工作表的最后
    Debug.Print Chr(10) & "--- 步骤: 创建新的工作表 'Plan' ---"
    Set ws = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    ws.Name = "Plan"
    Debug.Print "  - 工作表 'Plan' 创建成功。"

    ' 设置新工作表的所有单元格背景色为白色，文本水平居中对齐
    ws.Cells.Interior.Color = RGB(255, 255, 255)
    ws.Cells.HorizontalAlignment = xlCenter
    Debug.Print "  - 已对新工作表进行基础格式设置 (背景色白色，文本居中)。"

    ' 设置甘特图表头的起始位置在 B2 单元格 (严格遵循您提供的 CreateGanttChartHeader_v1 代码)
    Set headerRange = ws.Range("B2")
    Debug.Print Chr(10) & "--- 步骤: 初始化表头生成参数 ---"
    Debug.Print "  - 表头写入起始单元格 headerRange 已设置为: " & headerRange.Address(False, False) ' 输出相对地址更清晰


    ' 硬编码设置项目的开始日期和结束日期 (使用您新提供的日期)
    pj_startDate = DateValue("12/21/2023")
    pj_endDate = DateValue("1/1/2024")
    Debug.Print "  - 项目定义开始日期 pj_startDate: " & Format(pj_startDate, "yyyy-mm-dd")
    Debug.Print "  - 项目定义结束日期 pj_endDate: " & Format(pj_endDate, "yyyy-mm-dd")

    ' 设置列宽的调整系数
    col_Width_factor = 1
    Debug.Print "  - 列宽调整系数 col_Width_factor 已设置为: " & col_Width_factor

    ' Set the start month and end month based on the given start and end dates
    ' 计算生成日历表头的起始日期（项目开始日所在月份的第一天）
    startDate = DateSerial(Year(pj_startDate), Month(pj_startDate), 1)
    ' 计算生成日历表头的结束日期（项目结束日所在月份的最后一天）
    endDate = DateSerial(Year(pj_endDate), Month(pj_endDate) + 1, 0)
    Debug.Print "  - 生成日历范围起始日期 (月首): " & Format(startDate, "yyyy-mm-dd")
    Debug.Print "  - 生成日历范围结束日期 (月末): " & Format(endDate, "yyyy-mm-dd")


    ' 初始化循环日期为日历范围起始日期 (startDate) 所在日历周（周一为一周开始，WeekNum(,2)的标准）的星期一
    ' Weekday(startDate, vbMonday): 1=周一, 7=周日
    ' startDate + 1 - Weekday(startDate, vbMonday): 如果 startDate 是周一，Weekday返回1，switchDate = startDate + 1 - 1 = startDate
    ' 如果 startDate 是周三，Weekday返回3，switchDate = startDate + 1 - 3 = startDate - 2 (即回到周一)
    switchDate = startDate + 1 - Weekday(startDate, vbMonday)
     ' 如果计算出的周一在 startDate 之前太多（比如上年），确保至少从包含 startDate 的周一开始
     ' 这是一个对原代码初始化逻辑的补充，确保处理范围正确
     If switchDate < startDate And Weekday(startDate, vbMonday) <> vbMonday Then
          switchDate = startDate + 1 - Weekday(startDate, vbMonday)
     ElseIf switchDate > startDate And Month(switchDate) <> Month(startDate) And Day(startDate) > 1 Then
         ' 如果计算出的周一跳到了下个月，但startDate在本月且不是月初第一天，可能需要回溯
         ' 这里的逻辑需要结合实际情况调整，原代码直接使用 startDate + 1 - Weekday(startDate, vbMonday)
         ' 为了严格遵循原代码，不修改 switchDate 的初始化方式，只加 Debug
     End If
    Debug.Print "  - 日历生成循环的初始日期 switchDate (基于 startDate 所在周周一): " & Format(switchDate, "yyyy-mm-dd")

    ' 初始化 Debug 辅助变量
    isFirstLoopIteration = True
    previousLoopYear = Year(switchDate) ' 用于判断是否跨年


    Debug.Print Chr(10) & "================================================================================"
    Debug.Print ">>> 开始主循环: 按周生成表头列 <<<"
    Debug.Print "================================================================================"

    ' 循环遍历，以周为单位生成表头列，直到 switchDate 超过结束日期 endDate
    Do While switchDate <= endDate

        ' --- 当前周循环迭代的 Debug 信息 ---
        Dim currentLoopYear As Integer
        currentLoopYear = Year(switchDate)

        Debug.Print Chr(10) & "--- 循环迭代开始 ---"
        Debug.Print "  - 当前处理周的起始日期 switchDate: " & Format(switchDate, "yyyy-mm-dd")
        Debug.Print "  - 当前 headerRange 写入基准位置: " & headerRange.Address(False, False)

        ' *** 替换 WeekNum 为 IsoWeekNum ***
        currentWeekNum = WorksheetFunction.IsoWeekNum(switchDate) ' 使用 switchDate 计算周数作为当前周的标识
        Debug.Print "  - 对应 ISO 周数 (以周一为始): 第 " & currentWeekNum & " 周"

        If isFirstLoopIteration Then
            Debug.Print "  - 提示: 这是日历生成循环处理的【第一周】。"
            isFirstLoopIteration = False
        End If

        ' 判断是否跨年
        If currentLoopYear <> previousLoopYear Then
            Debug.Print "  - 提示: 本周发生了【跨年】，从 " & previousLoopYear & " 年进入 " & currentLoopYear & " 年。"
        End If
        previousLoopYear = currentLoopYear ' 更新年份以供下次迭代判断

        Dim isCrossMonthInWeek As Boolean
        isCrossMonthInWeek = (Month(switchDate) <> Month(switchDate + 6))

        If isCrossMonthInWeek Then
            Dim endOfCurrentMonthInWeek As Date
            ' 找到 switchDate 所在月份的最后一天
            endOfCurrentMonthInWeek = DateSerial(Year(switchDate), Month(switchDate) + 1, 0)

            Dim daysInCurrentMonthPart As Integer
            Dim daysInNextMonthPart As Integer

            ' 计算当前月部分的天数 (从 switchDate 到月末)
            daysInCurrentMonthPart = endOfCurrentMonthInWeek - switchDate + 1
            ' 计算下个月部分的天数 (从下个月初到 switchDate + 6)
            daysInNextMonthPart = (switchDate + 6) - endOfCurrentMonthInWeek

            Debug.Print "  - 提示: 本周【跨月】，从 " & Month(switchDate) & "月 (" & Format(switchDate, "yyyy-mm") & ") 到 " & Month(switchDate + 6) & "月 (" & Format(switchDate + 6, "yyyy-mm") & ")。"
            Debug.Print "    - 天数分布: 本月部分有 " & daysInCurrentMonthPart & "天，下月部分有 " & daysInNextMonthPart & "天。"
        Else
            Debug.Print "  - 提示: 本周【不跨月】，完整位于 " & Month(switchDate) & "月 (" & Format(switchDate, "yyyy-mm") & ")。"
        End If


        With headerRange ' 在当前 headerRange 指向的单元格为基准进行操作

            ' --- 判断并处理不同类型的周 (遵循原代码的 If/ElseIf 结构) ---

            ' 判断是否为跨月周
            If isCrossMonthInWeek Then

                ' 跨月周类型 1: 跨月 - 第一次运行 (原代码逻辑: 月份跨越 + 当前周的周一 switchDate 在项目开始日期 startDate 之前)
                If (Month(switchDate) Mod 12) + 1 = Month(startDate) And switchDate < startDate Then
                    Debug.Print "  - 处理分支: 【跨月且是日历起始周】 (switchDate < startDate)。"
                    ' 写入周数、月份、年份到相对于 headerRange 向下偏移并向右偏移 1 列的位置 (相对于 B2 的 Offset(4,1) -> C6, Offset(3,1) -> C5, Offset(2,1) -> C4)
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate + 6)
                    .Offset(4, 1).NumberFormat = "00"
                    .Offset(3, 1).Value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00"
                    .Offset(2, 1).Value = Year(switchDate + 6)
                    Debug.Print "    - 写入数据: 周 " & .Offset(4, 1).Value & ", 月 " & .Offset(3, 1).Value & ", 年 " & .Offset(2, 1).Value
                    Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 1), .Offset(4, 1)).Address(False, False)

                    ' 设置该列的宽度，基于 startDate 到周日的天数 乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    Debug.Print "    - 设置列 " & .Offset(4, 1).EntireColumn.Address(False, False) & " 的宽度，基于天数 " & (7 - Weekday(startDate, vbMonday) + 1) & " 乘以系数。"

                    ' --- headerRange 在分支内部右移 1 列 ---
                    Set headerRange = headerRange.Offset(0, 1)
                    Debug.Print "  - headerRange 在分支内部【第一次右移】至: " & headerRange.Address(False, False)


                ' 跨月周类型 2: 跨月 - 最后一周 (原代码逻辑: 当前周的周一 switchDate 等于项目结束日所在周的周一)
                ElseIf switchDate = endDate - Weekday(endDate, vbMonday) + 1 Then
                     Debug.Print "  - 处理分支: 【跨月且是日历结束周】 (switchDate 等于 endDate 所在周的周一)。"
                    ' 写入周数、月份、年份到相对于 headerRange 向下偏移的位置 (相对于 B2 的 Offset(4,0) -> B6, Offset(3,0) -> B5, Offset(2,0) -> B4)
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 0).NumberFormat = "00"
                    .Offset(3, 0).Value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"
                    .Offset(2, 0).Value = Year(switchDate)
                     Debug.Print "    - 写入数据: 周 " & .Offset(4, 0).Value & ", 月 " & .Offset(3, 0).Value & ", 年 " & .Offset(2, 0).Value
                     Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 0), .Offset(4, 0)).Address(False, False)

                    ' 设置该列的宽度，基于项目结束日是周内的第几天 乘以系数
                    .EntireColumn.ColumnWidth = Weekday(endDate, vbMonday) * col_Width_factor
                    Debug.Print "    - 设置列 " & .EntireColumn.Address(False, False) & " 的宽度，基于天数 " & Weekday(endDate, vbMonday) & " 乘以系数。"
                    ' 注意: 此分支【没有】在内部移动 headerRange


                ' 跨月周类型 3: 跨月 - 中间周 (原代码逻辑: switchDate 在 startDate 之后 且 switchDate 在项目结束周的周一之前)
                ElseIf switchDate > startDate And switchDate < endDate - Weekday(endDate, vbMonday) + 1 Then
                    Debug.Print "  - 处理分支: 【中间跨月周】 (完整周跨月，拆分为两列)。"

                    ' 写入当前月部分数据到当前列 (Offset(x,0))
                    ' 注意：原始代码 WeekNum 使用 DateSerial(Year(switchDate), Month(switchDate) + 1, 0) 计算
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(DateSerial(Year(switchDate), Month(switchDate) + 1, 0))
                    .Offset(4, 0).NumberFormat = "00"
                    .Offset(3, 0).Value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"
                    .Offset(2, 0).Value = Year(switchDate)
                    ' 列宽计算基于下个月第一天是周几的天数 (即本月在此周的天数) 乘以系数
                    .Offset(4, 0).EntireColumn.ColumnWidth = Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) * col_Width_factor

                    Debug.Print "    - 写入部分 A (当前月): 周 " & .Offset(4, 0).Value & ", 月 " & .Offset(3, 0).Value & ", 年 " & .Offset(2, 0).Value
                    Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 0), .Offset(4, 0)).Address(False, False)
                    Debug.Print "      - 设置列 " & .Offset(4, 0).EntireColumn.Address(False, False) & " 宽度，基于天数 " & Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) & " 乘以系数。"


                    ' 写入下一个月部分数据到右边一列 (Offset(x,1))
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate + 6) ' 使用周日计算周数
                    .Offset(4, 1).NumberFormat = "00"
                    .Offset(3, 1).Value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00"
                    .Offset(2, 1).Value = Year(switchDate + 6)
                    ' 列宽计算基于 7 减去下个月第一天是周几的天数 (即下月在此周的天数) 乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) * col_Width_factor

                    Debug.Print "    - 写入部分 B (下月): 周 " & .Offset(4, 1).Value & ", 月 " & .Offset(3, 1).Value & ", 年 " & .Offset(2, 1).Value
                    Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 1), .Offset(4, 1)).Address(False, False)
                    Debug.Print "      - 设置列 " & .Offset(4, 1).EntireColumn.Address(False, False) & " 宽度，基于天数 " & (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) & " 乘以系数。"

                    ' --- headerRange 在分支内部右移 1 列 ---
                    Set headerRange = headerRange.Offset(0, 1)
                    Debug.Print "  - headerRange 在分支内部【第一次右移】至: " & headerRange.Address(False, False)

                End If ' End If for cross-month sub-branches

            ' 处理不跨月周 (else of If isCrossMonthInWeek)
            Else

                ' 不跨月周类型 1: 不跨月 - 第一次运行 (原代码逻辑: 当前周的周一 switchDate 等于 startDate)
                If switchDate = startDate Then
                    Debug.Print "  - 处理分支: 【不跨月且是日历起始周】 (switchDate 等于 startDate)。"
                    ' 写入周数、月份、年份到相对于 headerRange 向下偏移并向右偏移 1 列的位置 (相对于 B2 的 Offset(4,1) -> C6, Offset(3,1) -> C5, Offset(2,1) -> C4)
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 1).Value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 1).NumberFormat = "00"
                    .Offset(3, 1).Value = Month(switchDate)
                    .Offset(3, 1).NumberFormat = "00"
                    .Offset(2, 1).Value = Year(switchDate)
                    Debug.Print "    - 写入数据: 周 " & .Offset(4, 1).Value & ", 月 " & .Offset(3, 1).Value & ", 年 " & .Offset(2, 1).Value
                    Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 1), .Offset(4, 1)).Address(False, False)


                    ' 设置该列的宽度，基于 startDate 到周日的天数 乘以系数
                    .Offset(4, 1).EntireColumn.ColumnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    Debug.Print "    - 设置列 " & .Offset(4, 1).EntireColumn.Address(False, False) & " 的宽度，基于天数 " & (7 - Weekday(startDate, vbMonday) + 1) & " 乘以系数。"

                    ' --- headerRange 在分支内部右移 1 列 ---
                    Set headerRange = headerRange.Offset(0, 1)
                    Debug.Print "  - headerRange 在分支内部【第一次右移】至: " & headerRange.Address(False, False)


                ' 不跨月周类型 2: 完整的非跨月周 (原代码逻辑: else)
                Else
                    Debug.Print "  - 处理分支: 【完整的非跨月周】 (标准 7 天周)。"
                    ' 写入周数、月份、年份到相对于 headerRange 向下偏移的位置 (相对于 B2 的 Offset(4,0) -> B6, Offset(3,0) -> B5, Offset(2,0) -> B4)
                    ' *** 替换 WeekNum 为 IsoWeekNum ***
                    .Offset(4, 0).Value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 0).NumberFormat = "00"
                    .Offset(3, 0).Value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"
                    .Offset(2, 0).Value = Year(switchDate)
                    Debug.Print "    - 写入数据: 周 " & .Offset(4, 0).Value & ", 月 " & .Offset(3, 0).Value & ", 年 " & .Offset(2, 0).Value
                    Debug.Print "      到单元格区域: " & ws.Range(.Offset(2, 0), .Offset(4, 0)).Address(False, False)

                    ' 设置该列的宽度为标准宽度（7天乘以系数）
                    .EntireColumn.ColumnWidth = 7 * col_Width_factor
                    Debug.Print "    - 设置列 " & .EntireColumn.Address(False, False) & " 的宽度，基于完整周 7 天乘以系数。"
                    ' 注意: 此分支【没有】在内部移动 headerRange

                End If ' End If for non-cross-month sub-branches

            End If ' End If isCrossMonthInWeek

        End With ' End With headerRange


        ' --- headerRange 在 Do While 循环末尾无条件右移 ---
        Set headerRange = headerRange.Offset(0, 1)
        Debug.Print "  - headerRange 在循环末尾【无条件第二次右移】至: " & headerRange.Address(False, False)

        ' 推进到下一个星期的星期一，准备处理下一周
        switchDate = switchDate + 7
        Debug.Print "--- 循环迭代结束，switchDate 已更新为下一周起始日期: " & Format(switchDate, "yyyy-mm-dd") & " ---"


    Loop ' 主循环结束
    Debug.Print Chr(10) & "================================================================================"
    Debug.Print ">>> 主循环结束: 表头列生成完毕 <<<"
    Debug.Print "================================================================================"


    ' 声明合并相关的变量
    Dim lastCol As Long
    Dim i As Integer, j As Integer
    Dim mergeRange As Range

    ' 确定表头数据写入的最右边的列（以表头第4行，即 B2 偏移 2 行的行作为参考，严格遵循原代码逻辑）
    lastCol = ws.Cells(4, ws.Columns.Count).End(xlToLeft).Column
    Debug.Print Chr(10) & "--- 步骤: 确定表头数据的最后一列 ---"
    Debug.Print "  - 基于第4行 (B2向下偏移2行) 确定最后一列为: " & ws.Cells(4, lastCol).Address(False, False)


    ' 关闭警告消息，以便进行单元格合并操作
    Application.DisplayAlerts = False
    Debug.Print Chr(10) & "--- 步骤: 关闭警告消息以便进行单元格合并 ---"

    Debug.Print Chr(10) & "================================================================================"
    Debug.Print ">>> 开始进行表头单元格合并 <<<"
    Debug.Print "================================================================================"

    ' 循环遍历需要合并的表头行：表头第4行（年份）、第5行（月份）、第6行（周数）
    ' 这些行对应于 headerRange (B2) 向下偏移 2, 3, 4 的行，即工作表的第 4, 5, 6 行
    ' 原始代码此处循环范围是 For i = 3 To 5，可能与 B2 Offset 后的行不一致，但遵循原代码
    Debug.Print "  - 将对工作表的第 3, 4, 5 行进行合并 (根据原代码 For i=3 To 5 的循环范围)。"
    For i = 3 To 5
        Debug.Print Chr(10) & "--- 正在处理行 " & i & " 的合并 ---"
        Set mergeRange = Nothing ' 每处理新的一行，重置合并范围
        Debug.Print "  - 初始化行 " & i & " 的合并范围为空。"

        ' 从第3列 (C列，因为 headerRange 是 B2，第一个数据列从 B 列开始，合并从下一列 C 列开始) 开始遍历到最后一列
        For j = 3 To lastCol
            ' 如果当前合并范围不为空，并且当前单元格的值与前一个单元格的值相同 (忽略是否已合并，原代码没有检查 MergeCells)
            ' 注意: 原代码没有检查 ws.Cells(i, j).MergeCells，如果前一个单元格是合并区域的一部分，这里可能需要特殊处理。
            If Not mergeRange Is Nothing And ws.Cells(i, j).Value = ws.Cells(i, j - 1).Value Then
                 ' 扩展当前的合并范围 (不打印每次扩展的地址，否则输出太多)
                Set mergeRange = ws.Range(mergeRange, ws.Cells(i, j))
                ' Debug.Print "  - 行 " & i & ", 列 " & j & " (" & ws.Cells(i, j).Address(False, False) & ")：值与前一单元格相同，合并范围扩展至: " & mergeRange.Address(False, False) ' 如果需要看每次扩展的地址，可以取消注释
            Else
                ' 如果当前单元格与前一个单元格的值不同，或者开始一个新的合并范围
                If Not mergeRange Is Nothing Then
                    ' 如果之前的合并范围包含多于一个单元格，则执行合并
                    If mergeRange.Columns.Count > 1 Then
                        Debug.Print "  - 行 " & i & ", 列 " & j & " (" & ws.Cells(i, j).Address(False, False) & ")：值与前一单元格不同 或 新范围开始。"
                        Debug.Print "    - 即将合并范围: " & mergeRange.Address(False, False)
                        mergeRange.Merge ' 执行合并
                        Debug.Print "    - 已成功合并范围: " & mergeRange.Address(False, False)
                    Else
                        ' 如果之前的范围只有一个单元格，无需合并
                        ' Debug.Print "  - 行 " & i & ", 列 " & j & " (" & ws.Cells(i, j).Address(False, False) & ")：前一个范围 (" & mergeRange.Address(False, False) & ") 只有一个单元格，无需合并。"
                    End If
                End If
                ' 将当前的单元格设置为新的合并范围的起始
                Set mergeRange = ws.Cells(i, j)
                Debug.Print "  - 行 " & i & ", 列 " & j & " (" & ws.Cells(i, j).Address(False, False) & ")：新的合并范围起始于此。"
            End If
        Next j ' 列循环结束

        ' 循环结束后，检查并合并最后一个累积的合并范围
        If Not mergeRange Is Nothing And mergeRange.Columns.Count > 1 Then
            Debug.Print "  - 行 " & i & "：列循环结束。即将合并最后一个范围: " & mergeRange.Address(False, False)
            mergeRange.Merge ' 执行合并
            Debug.Print "  - 行 " & i & "：已成功合并最后一个范围: " & mergeRange.Address(False, False)
        ElseIf Not mergeRange Is Nothing Then
             ' 如果最后一个范围只有一个单元格
             ' Debug.Print "  - 行 " & i & "：列循环结束。最后一个范围 (" & mergeRange.Address(False, False) & ") 只有一个单元格，无需合并。"
        End If
        Debug.Print "--- 行 " & i & " 的合并处理完成 ---"

    Next i ' 行循环结束
    Debug.Print Chr(10) & "================================================================================"
    Debug.Print ">>> 表头单元格合并完成 <<<"
    End Sub
