# DetermineTaskRowAndCategory 函数说明

## 功能概述

`DetermineTaskRowAndCategory` 函数是甘特图系统中的核心函数之一，负责两个关键功能：
1. 确定任务在甘特图上的行位置
2. 处理任务的类别信息并在适当的位置添加类别标题

该函数整合了原来分离的 `DetermineTaskRow` 和 `ProcessTaskCategory` 函数的功能，通过统一处理行位置和类别信息，简化了代码结构，减少了重复逻辑。

## 函数签名

```vba
Private Function DetermineTaskRowAndCategory(task As Dictionary, ws As Worksheet, currentRow As Long, lastTaskRow As Long, _
                                           taskIndex As Long, ByRef currentCategory As String, _
                                           ByRef isCurrentRowCategorySet As Boolean) As Long
```

## 参数说明

| 参数 | 类型 | 传递方式 | 说明 |
|------|------|----------|------|
| task | Dictionary | 值 | 当前处理的任务（Dictionary对象） |
| ws | Worksheet | 值 | 甘特图工作表引用 |
| currentRow | Long | 值 | 当前处理行 |
| lastTaskRow | Long | 值 | 上一个任务的行位置 |
| taskIndex | Long | 值 | 任务在集合中的索引（从1开始） |
| currentCategory | String | 引用 | 当前类别（可被修改） |
| isCurrentRowCategorySet | Boolean | 引用 | 当前行的类别是否已设置（可被修改） |

## 返回值

- 返回一个Long类型的值，表示任务应该放置的行号

## 处理流程

函数的处理流程基于taskIndex（任务索引）分为两大类：

### 处理第一个任务（taskIndex = 1）

1. **获取任务的Position和Category属性**
   - 检查任务是否有Position属性，如果没有，使用默认值"next"
   - 检查任务是否有Category属性，如果没有，设置为空字符串

2. **根据Position类型确定行位置**
   - **如果Position是"same"或0**：
     - 首个任务不能使用"same"或0，默认使用"next"（currentRow + 1）

   - **如果Position是"next"**：
     - 使用当前行位置加1（currentRow + 1）

   - **如果Position是数字**：
     - 使用当前行位置加上Position值（currentRow + CInt(taskPosition)）
     - 如果结果小于最小行位置（第6行），弹窗报错并直接退出函数，返回-1表示错误

   - **其他情况**：
     - 默认使用当前行位置加1（currentRow + 1）

3. **处理类别信息**
   - 重置行类别设置状态（isCurrentRowCategorySet = False）
   - 如果任务有非空类别：
     - 设置当前类别（currentCategory = taskCategory）
     - 标记当前行类别已设置（isCurrentRowCategorySet = True）
     - 添加类别标题：
       - 在resultRow行、第2列添加类别文本
       - 设置文本为粗体，字体大小11，左对齐，垂直居中

### 处理后续任务（taskIndex > 1）

1. **根据Position类型确定行位置和处理类别**
   - **如果Position是"same"或0**：
     - 使用上一任务的行位置（lastTaskRow）
     - 如果当前行的类别尚未设置且任务有非空类别：
       - 设置当前类别（currentCategory = taskCategory）
       - 标记当前行类别已设置（isCurrentRowCategorySet = True）
       - 添加类别标题（格式同上）

   - **如果Position是"next"**：
     - 使用当前行位置加1（currentRow + 1）
     - 重置行类别设置状态（isCurrentRowCategorySet = False）
     - 如果任务有非空类别：
       - 设置当前类别（currentCategory = taskCategory）
       - 标记当前行类别已设置（isCurrentRowCategorySet = True）
       - 添加类别标题（格式同上）

   - **如果Position是数字**：
     - 使用当前行位置加上Position值（currentRow + CInt(taskPosition)）
     - 如果结果小于最小行位置（第6行），弹窗报错并直接退出函数，返回-1表示错误
     - 重置行类别设置状态（isCurrentRowCategorySet = False）
     - 如果任务有非空类别：
       - 设置当前类别（currentCategory = taskCategory）
       - 标记当前行类别已设置（isCurrentRowCategorySet = True）
       - 添加类别标题（格式同上）

   - **其他情况**：
     - 默认使用当前行位置加1（currentRow + 1）
     - 重置行类别设置状态（isCurrentRowCategorySet = False）
     - 如果任务有非空类别：
       - 设置当前类别（currentCategory = taskCategory）
       - 标记当前行类别已设置（isCurrentRowCategorySet = True）
       - 添加类别标题（格式同上）

## 流程图

```mermaid
flowchart TD
    A[开始] --> B[获取任务Position和Category属性]
    B --> C{taskIndex = 1?}

    %% 处理第一个任务
    C -->|是| D{Position是'same'或0?}
    D -->|是| G[使用currentRow+1]
    D -->|否| F{Position是'next'?}
    F -->|是| G[使用currentRow+1]
    F -->|否| H{Position是数字?}
    H -->|是| I[使用currentRow+Position]
    H -->|否| J[默认使用currentRow+1]
    I --> K{resultRow < 6?}
    K -->|是| L[弹窗报错并返回-1]
    K -->|否| M[重置isCurrentRowCategorySet]
    G --> M
    J --> M
    M --> N{taskCategory不为空?}
    N -->|是| O[设置currentCategory并添加类别标题]
    N -->|否| P[返回resultRow]
    O --> P

    %% 处理后续任务
    C -->|否| Q{Position是'same'或0?}
    Q -->|是| R[使用lastTaskRow]
    R --> S{isCurrentRowCategorySet为False且taskCategory不为空?}
    S -->|是| T[设置currentCategory并添加类别标题]
    S -->|否| U[返回resultRow]
    T --> U

    Q -->|否| V{Position是'next'?}
    V -->|是| W[使用currentRow+1]
    W --> X[重置isCurrentRowCategorySet]
    X --> Y{taskCategory不为空?}
    Y -->|是| Z[设置currentCategory并添加类别标题]
    Y -->|否| U
    Z --> U

    V -->|否| AA{Position是数字?}
    AA -->|是| AB[使用currentRow+Position]
    AB --> AC{resultRow < 6?}
    AC -->|是| L
    AC -->|否| AD[重置isCurrentRowCategorySet]
    AD --> AE{taskCategory不为空?}
    AE -->|是| AF[设置currentCategory并添加类别标题]
    AE -->|否| U
    AF --> U

    AA -->|否| AG[默认使用currentRow+1]
    AG --> AH[重置isCurrentRowCategorySet]
    AH --> AI{taskCategory不为空?}
    AI -->|是| AJ[设置currentCategory并添加类别标题]
    AI -->|否| U
    AJ --> U

    L --> AK[结束]
    P --> AK
    U --> AK
```

## 优化说明

1. **基于taskIndex的处理流程**：
   - 将处理流程分为"首个任务"和"后续任务"两大类
   - 直接使用taskIndex判断是否是首个任务，消除了isFirstTaskProcessed标记
   - 在每个分支内处理相关的Position和Category，减少了条件判断的嵌套
   - 更加线性和直观的处理流程

2. **合并相同逻辑**：
   - 将"same"和0的处理逻辑合并为一个条件块
   - 减少了代码重复，使逻辑更加清晰
   - 如果需要修改"same"和0的处理逻辑，只需要修改一处

3. **行位置计算优化**：
   - 当行位置计算结果小于6时，直接弹窗报错并退出函数
   - 返回-1表示错误，便于调用方处理
   - 不再自动调整行位置，而是要求用户修正输入

4. **类别处理优化**：
   - 为类别标题设置统一的格式：粗体，字体大小11，左对齐，垂直居中
   - 在每个Position处理分支内直接处理Category，减少了重复的条件判断
   - 对于首个任务，不允许使用"same"或0作为Position值，默认使用"next"

5. **关键设计原则**：
   - **Position属性的处理规则**：
     - "same"或数字0: 与上一任务同行（第一个任务除外）
     - "next": 移动到下一行
     - 其他数字: 移动指定的行数
     - 无效值: 默认移动到下一行
     - 首个任务不能使用"same"或0，默认使用"next"

   - **类别继承机制**：
     - 如果当前行已设置类别，后续同行的任务将继承该类别
     - 只有新行的第一个任务有机会设置该行的类别

   - **类别标题显示规则**：
     - 首个任务：如果有非空类别，添加类别标题
     - 后续任务：根据Position类型和isCurrentRowCategorySet状态决定是否添加类别标题
     - 类别标题显示在第2列，使用粗体，字体大小11，左对齐，垂直居中

6. **错误处理**：
   - 添加了完整的错误处理机制（On Error GoTo ErrorHandler）
   - 对于行位置小于6的情况，弹窗报错并直接退出函数
   - 返回-1表示错误，便于调用方处理
   - 在DrawTasksAndMilestones函数中检查返回值，如果为-1则中断甘特图生成

## 与其他函数的交互

- **与DrawTasksAndMilestones的关系**：
  - `DrawTasksAndMilestones`调用`DetermineTaskRowAndCategory`获取任务的行位置和处理类别信息
  - 检查返回值，如果为-1则表示出错，中断甘特图生成
  - 获取行位置后，`DrawTasksAndMilestones`更新currentRow和lastTaskRow变量
  - 如果添加了类别标题，`DrawTasksAndMilestones`会相应地调整当前行

## 使用示例

```vba
' 在DrawTasksAndMilestones函数中的调用
Dim taskRow As Long
taskRow = DetermineTaskRowAndCategory(task, ws, currentRow, lastTaskRow, i, currentCategory, isCurrentRowCategorySet)

' 检查是否发生错误（返回-1）
If taskRow = -1 Then
    modDebug.LogError 1001, "任务 " & taskId & " 的行位置确定失败，中断甘特图生成", "modGantt.DrawTasksAndMilestones"
    modDebug.LogFunctionExit "modGantt.DrawTasksAndMilestones", "失败 - 任务行位置确定失败"
    Exit Sub
End If

' 检查是否需要更新当前行（如果是类别标题行）
If taskRow <> currentRow And task.Exists("Category") And task("Category") <> "" Then
    ' 如果是新行且有类别，需要移动到下一行（类别标题占用了当前行）
    currentRow = taskRow + 1
Else
    ' 否则，使用任务行作为当前行
    currentRow = taskRow
End If

' 更新上一任务行
lastTaskRow = taskRow
```
