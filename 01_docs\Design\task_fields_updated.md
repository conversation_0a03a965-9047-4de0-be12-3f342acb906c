# 任务/里程碑工作表字段定义

**工作表名称**: `Milestones&WBS`
**超级表名称**: `taskTable`

## 1. 基本字段定义

| 字段名 | 数据类型 | 说明 | 是否必填 | 定义名称 | 默认值 |
| ------ | -------- | ---- | -------- | -------- | ------ |
| ID | 文本/数字 | 任务/里程碑的唯一标识符（可通过公式自动生成） | 是 | taskId | 自动生成 |
| Category | 文本 | 所属父任务的大类，如"项目启动" | 是 | taskCategory | 无 |
| Description | 文本 | 任务/里程碑的描述 | 否 | taskDescription | 无 |
| Type | 文本 | A/M（A:activity任务活动，M:milestone里程碑） | 是 | taskType | 无 |
| Start Date | 日期 | 任务开始的时间 | 是 | taskStartDate | 无 |
| End Date | 日期 | 任务结束的时间（里程碑只参照开始日期） | 条件必填 | taskEndDate | 对于里程碑，等于开始日期 |
| Duration | 数字 | 任务持续时间（天） | 自动计算 | taskDuration | 自动计算 |
| Progress | 百分比 | 完成百分比（0-100%） | 否 | taskProgress | 0 |
| Position | 文本/数字 | 相对于上一行的位置（same/next/数字） | 否 | taskPosition | next |
| Color | 文本/颜色代码 | 任务条/里程碑的填充颜色（支持十六进制颜色代码如#3366CC，或预定义代码G/Y/R/S） | 否 | taskColor | 任务:#3366CC<br>里程碑:#FF9900 |
| Text Position | 文本 | 文字相对于任务条/里程碑的位置（left/right/inside/above/below） | 否 | taskTextPosition | 任务:right<br>里程碑:right |
| Baseline | 日期 | 基准线日期，在甘特图中显示为垂直虚线 | 否 | taskBaseline | 无 |
| ShowDateInLabel | 文本 | 是否在标签中显示日期（Y=显示，空白=不显示） | 否 | taskShowDateInLabel | 空白（不显示） |
| Mark | 任意值 | 标记列，用于控制是否绘制该任务（非空才绘制，仅用于过滤） | 否 | taskMark | 无 |

## 2. 字段处理逻辑

### 2.1 必填字段验证

系统会验证以下必填字段：
- ID：必须唯一
- Type：必须为"A"或"M"
- Start Date：必须为有效日期
- End Date：对于任务（Type="A"）必须为有效日期且不早于开始日期

**注意**：Category和Description字段在当前版本中已改为可选字段。

### 2.2 自动计算字段

- **Duration**：
  - 对于任务（Type="A"）：根据Start Date和End Date自动计算工作日数量
  - 对于里程碑（Type="M"）：固定为0
  - 计算时可配置是否排除周末（ExcludeWeekends配置项）

### 2.3 可选字段默认值

- **Progress**：默认为0（0%）
- **Position**：默认为"next"（在上一行任务之后）
- **Color**：
  - 任务默认为#3366CC（蓝色）
  - 里程碑默认为#FF9900（橙色）
  - 支持预定义代码：G(绿色)、Y(黄色)、R(红色)、S(紫色)
- **Text Position**：
  - 任务默认为"right"（文字在任务条右侧）
  - 里程碑默认为"right"（文字在里程碑右侧）

### 2.4 Mark列过滤机制

**Mark列**是一个特殊的过滤列，用于控制哪些任务会被绘制到甘特图中：

- **存在Mark列且非空**：该行任务会被包含在甘特图绘制中
- **存在Mark列但为空**：该行任务会被跳过，不会出现在甘特图中
- **不存在Mark列**：所有任务行都会被处理（向后兼容）

**使用场景**：
- 项目阶段性展示：只显示当前阶段的任务
- 任务筛选：根据优先级或状态筛选任务
- 自定义视图：用户可以灵活控制甘特图的内容

**实现逻辑**：
```vba
' 检查Mark列过滤条件（如果Mark列存在）
If hasMarkCol Then
    ' 如果Mark列存在且该行的Mark列为空，则跳过此行
    If IsEmpty(dataArray(i, markColPos)) Or dataArray(i, markColPos) = "" Then
        GoTo NextIteration ' 跳过当前行
    End If
End If
```

## 3. 数据访问优化

为提高性能，系统使用数组优化访问任务数据：

```vba
' 一次性读取整个数据区域到数组
Dim dataArray As Variant
dataArray = dataRange.Value
Dim rowCount As Long
rowCount = UBound(dataArray, 1)

' 遍历数组处理数据
For i = 1 To rowCount
    Set task = New Dictionary

    ' 从数组中获取数据，使用列的相对位置
    task.Add "ID", dataArray(i, 1)
    task.Add "Category", dataArray(i, 2)
    ' ... 其他字段
Next i
```
