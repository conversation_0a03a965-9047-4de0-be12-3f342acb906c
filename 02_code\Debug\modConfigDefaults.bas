'Attribute VB_Name = "modConfigDefaults"
Option Explicit

' 获取所有配置项的默认值
Public Function GetDefaults() As Dictionary
    Dim defaults As New Dictionary

    '===== UI相关默认值 =====

    ' 项目名称区域配置
    defaults.Add "ProjectNameFont", "Barlow"
    defaults.Add "ProjectNameFontSize", 18
    defaults.Add "ProjectNameFontBold", True
    defaults.Add "ProjectNameFontColor", "#D3E7E3"
    defaults.Add "ProjectNameBackColor", "#EB5A50"
    defaults.Add "ProjectNameBorderStyle", "none"
    defaults.Add "ProjectNameRowHeight", "auto"
    defaults.Add "ProjectNameTextAlign", "left"

    ' 项目经理名称区域配置
    defaults.Add "ProjectManagerFont", "Barlow"
    defaults.Add "ProjectManagerFontSize", 11
    defaults.Add "ProjectManagerFontBold", False
    defaults.Add "ProjectManagerFontColor", "#D3E7E3"
    defaults.Add "ProjectManagerBackColor", "#EB5A50"
    defaults.Add "ProjectManagerBorderStyle", "none"
    defaults.Add "ProjectManagerRowHeight", "auto"
    defaults.Add "ProjectManagerTextAlign", "left"

    ' 补充信息区域配置
    defaults.Add "SupplementInfoFont", "Barlow"
    defaults.Add "SupplementInfoFontSize", 9
    defaults.Add "SupplementInfoFontBold", False
    defaults.Add "SupplementInfoFontColor", "#FFFFFF"
    defaults.Add "SupplementInfoBackColor", "#EB5A50"
    defaults.Add "SupplementInfoBorderStyle", "none"
    defaults.Add "SupplementInfoRowHeight", "auto"

    ' 时间轴表头年区域配置
    defaults.Add "TimelineYearFont", "Arial"
    defaults.Add "TimelineYearFontSize", 11
    defaults.Add "TimelineYearFontBold", True
    defaults.Add "TimelineYearFontColor", "#FFFFFF"
    defaults.Add "TimelineYearBackColor", "#EB5A50"
    defaults.Add "TimelineYearBorderStyle", "all"
    defaults.Add "TimelineYearRowHeight", "auto"

    ' 时间轴表头月区域配置
    defaults.Add "TimelineMonthFont", "Arial"
    defaults.Add "TimelineMonthFontSize", 10
    defaults.Add "TimelineMonthFontBold", True
    defaults.Add "TimelineMonthFontColor", "#FFFFFF"
    defaults.Add "TimelineMonthBackColor", "#EB5A50"
    defaults.Add "TimelineMonthBorderStyle", "all"
    defaults.Add "TimelineMonthRowHeight", "auto"

    ' 时间轴表头周区域配置
    defaults.Add "TimelineWeekFont", "Arial"
    defaults.Add "TimelineWeekFontSize", 9
    defaults.Add "TimelineWeekFontBold", False
    defaults.Add "TimelineWeekFontColor", "#FFFFFF"
    defaults.Add "TimelineWeekBackColor", "#EB5A50"
    defaults.Add "TimelineWeekBorderStyle", "all"
    defaults.Add "TimelineWeekRowHeight", "auto"

    ' 任务类区域配置
    defaults.Add "CategoryFont", "Barlow"
    defaults.Add "CategoryFontSize", 11
    defaults.Add "CategoryFontBold", True
    defaults.Add "CategoryFontColor", "#000000"
    defaults.Add "CategoryBackColor", "#F2F2F2"
    defaults.Add "CategoryBorderStyle", "bottom"
    defaults.Add "CategoryColumnWidth", 30
    defaults.Add "CategoryWrapText", True
    defaults.Add "CategoryTextAlign", "center"

    ' 全局配置
    defaults.Add "ChartTheme", "custom"
    defaults.Add "ChartGridlinesArea", "all"
    defaults.Add "ChartGridlinesType", "all"
    defaults.Add "ChartGridlineColor", "#DDDDDD"
    defaults.Add "ChartBackgroundColor", "#FFFFFF"
    defaults.Add "ChartAlternateRowColor", "#F5F5F5"
    defaults.Add "ColumnAWidth", 2

    ' Logo相关配置
    defaults.Add "EnableLogo", True
    defaults.Add "LogoMargin", 2

    '===== Gantt相关默认值 =====

    ' 布局相关
    defaults.Add "CellWidthFactor", 0.3
    defaults.Add "DefaultTaskPosition", "next"
    defaults.Add "DefaultTaskTextPosition", "right"
    defaults.Add "DefaultMilestoneTextPosition", "right"
    defaults.Add "TaskBarHeight", 11
    defaults.Add "LabelDistance", 5
    defaults.Add "RowPadding", 3

    ' 颜色相关
    defaults.Add "DefaultTaskColor", "#3366CC"
    defaults.Add "DefaultMilestoneColor", "#FF9900"
    defaults.Add "DefaultColorG", "#00FF00"  ' 绿色
    defaults.Add "DefaultColorY", "#FFFF00"  ' 黄色
    defaults.Add "DefaultColorR", "#FF0000"  ' 红色
    defaults.Add "DefaultColorS", "#800080"  ' 紫色
    defaults.Add "TaskProgressColor", "#66CC66"
    defaults.Add "CurrentDateLineColor", "#41B7AC"
    defaults.Add "ProgressBarColor", "#66CC66"

    ' 样式相关
    defaults.Add "EnableCurrentDateLine", False
    defaults.Add "CurrentDateLineStyle", 2
    defaults.Add "CurrentDateLineWeight", 0.8
    defaults.Add "BaselineColor", "#FF0000"
    defaults.Add "BaselineStyle", 2
    defaults.Add "BaselineWeight", 0.8
    defaults.Add "TaskBarBorderWidth", 0
    defaults.Add "MilestoneShapeStyle", 1 ' 1=菱形，更多选项见config_table.txt
    defaults.Add "TaskBarShapeStyle", 1

    ' 标签字体相关
    defaults.Add "TaskLabelFont", "Arial"
    defaults.Add "TaskLabelFontSize", 8
    defaults.Add "MilestoneLabelFont", "Arial"
    defaults.Add "MilestoneLabelFontSize", 8

    ' 聚光灯效果相关
    defaults.Add "EnableSpotlight", True
    defaults.Add "SpotlightMode", "all" ' all=水平和垂直/horizontal=仅水平/vertical=仅垂直
    defaults.Add "SpotlightColor", "#E6F2FF" ' 浅蓝色

    ' 甘特图外边框相关
    defaults.Add "EnableGanttBorder", True
    defaults.Add "GanttBorderColor", "#D3D3D3" ' 浅灰色
    defaults.Add "GanttBorderWeight", 1 ' 1=细
    defaults.Add "GanttBorderStyle", 1 ' 1=实线



    '===== Data相关默认值 =====

    defaults.Add "AutoCalculateDuration", True
    defaults.Add "ExcludeWeekends", True
    defaults.Add "DefaultTaskProgress", 0

    '===== Debug相关默认值 =====

    ' 是否开启debug模式
    defaults.Add "EnableDebug", False

    ' debug日志级别, 默认4 (DEBUG_LEVEL_VERBOSE)
    defaults.Add "DebugLevel", 4

    ' 是否启用文件日志
    defaults.Add "EnableFileLogging", False

    ' 是否输出到即时窗口
    defaults.Add "EnableImmediateOutput", False

    ' 文件编码设置
    defaults.Add "UTF8Encoding", True

    Set GetDefaults = defaults
End Function
